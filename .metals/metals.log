2025.07.23 09:11:46 INFO  Started: Metals version 1.6.0 in folders '/home/<USER>/projects/match-manager' for client Visual Studio Code - Insiders 1.103.0-insider.
2025.07.23 09:11:47 INFO  running '/usr/bin/sbt -Dbloop.export-jar-classifiers=sources bloopInstall'
Jul 23, 2025 9:11:47 AM io.undertow.Undertow start
INFO: starting server: Undertow - 2.3.12.Final
Jul 23, 2025 9:11:47 AM org.xnio.Xnio <clinit>
INFO: XNIO version 3.8.16.Final
Jul 23, 2025 9:11:47 AM org.xnio.nio.NioXnio <clinit>
INFO: XNIO NIO Implementation Version 3.8.16.Final
Jul 23, 2025 9:11:47 AM org.jboss.threads.Version <clinit>
INFO: JBoss Threads version 3.5.0.Final
2025.07.23 09:11:47 INFO  Metals MCP server started on port: 45211.
2025.07.23 09:11:48 INFO  no build target found for /home/<USER>/projects/match-manager/project/project/metals.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.07.23 09:11:48 WARN  no build target for: /home/<USER>/projects/match-manager/project/metals.sbt
2025.07.23 09:11:47 WARN  no build target for: /home/<USER>/projects/match-manager/project/project/metals.sbt
Jul 23, 2025 9:11:48 AM org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint notify
INFO: Unsupported notification method: $/setTrace
2025.07.23 09:11:48 INFO  restarted 1 presentation compiler
2025.07.23 09:11:47 INFO  no build target found for /home/<USER>/projects/match-manager/project/metals.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.07.23 09:11:48 INFO  [info] welcome to sbt 1.6.1 (Eclipse Adoptium Java 17.0.15)
2025.07.23 09:11:50 INFO  [info] loading settings for project match-manager-build-build-build from metals.sbt ...
2025.07.23 09:11:50 INFO  [info] loading project definition from /home/<USER>/projects/match-manager/project/project/project
2025.07.23 09:11:51 INFO  [info] loading settings for project match-manager-build-build from metals.sbt ...
2025.07.23 09:11:51 INFO  [info] loading project definition from /home/<USER>/projects/match-manager/project/project
2025.07.23 09:11:52 INFO  [success] Generated .bloop/match-manager-build-build.json
2025.07.23 09:11:52 INFO  [success] Total time: 1 s, completed Jul 23, 2025, 9:11:52 AM
2025.07.23 09:11:52 INFO  [info] loading settings for project match-manager-build from metals.sbt,plugins.sbt ...
2025.07.23 09:11:52 INFO  [info] loading project definition from /home/<USER>/projects/match-manager/project
2025.07.23 09:11:55 INFO  [success] Generated .bloop/match-manager-build.json
2025.07.23 09:11:55 INFO  [success] Total time: 2 s, completed Jul 23, 2025, 9:11:55 AM
2025.07.23 09:11:57 INFO  [info] loading settings for project root from build.sbt,deploy.sbt,docker.sbt ...
2025.07.23 09:11:57 INFO  [info] set current project to match-manager (in build file:/home/<USER>/projects/match-manager/)
2025.07.23 09:11:57 INFO  [info] .env detected (fileName=.env). About to configure JVM System Environment with new map: Map(DATABASE_USER -> (your database user for the test environment), DATABASE_PASSWORD -> (your password for the test environment), DATABASE_CONNECTION_STRING -> *****************************************************************************************************************)
2025.07.23 09:11:57 INFO  [info] Configured .env environment
2025.07.23 09:11:57 ERROR java.lang.reflect.InaccessibleObjectException: Unable to make field private static final java.util.HashMap java.lang.ProcessEnvironment.theEnvironment accessible: module java.base does not "opens java.lang" to unnamed module @56c8c77e
2025.07.23 09:11:57 ERROR 	at java.base/java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:354)
2025.07.23 09:11:57 ERROR 	at java.base/java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:297)
2025.07.23 09:11:57 ERROR 	at java.base/java.lang.reflect.Field.checkCanSetAccessible(Field.java:178)
2025.07.23 09:11:57 ERROR 	at java.base/java.lang.reflect.Field.setAccessible(Field.java:172)
2025.07.23 09:11:57 ERROR 	at au.com.onegeek.sbtdotenv.DirtyEnvironmentHack$.$anonfun$setEnv$1(DirtyEnvironmentHack.scala:42)
2025.07.23 09:11:57 ERROR 	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)
2025.07.23 09:11:57 ERROR 	at scala.util.Try$.apply(Try.scala:213)
2025.07.23 09:11:57 ERROR 	at au.com.onegeek.sbtdotenv.DirtyEnvironmentHack$.setEnv(DirtyEnvironmentHack.scala:38)
2025.07.23 09:11:57 ERROR 	at au.com.onegeek.sbtdotenv.SbtDotenv$.applyEnvironment(SbtDotenv.scala:90)
2025.07.23 09:11:57 ERROR 	at au.com.onegeek.sbtdotenv.SbtDotenv$.$anonfun$configureEnvironment$2(SbtDotenv.scala:75)
2025.07.23 09:11:57 ERROR 	at scala.Option.fold(Option.scala:251)
2025.07.23 09:11:57 ERROR 	at au.com.onegeek.sbtdotenv.SbtDotenv$.configureEnvironment(SbtDotenv.scala:75)
2025.07.23 09:11:57 ERROR 	at au.com.onegeek.sbtdotenv.SbtDotenv$autoImport$.$anonfun$dotEnv$1(SbtDotenv.scala:43)
2025.07.23 09:11:57 ERROR 	at scala.Function1.$anonfun$compose$1(Function1.scala:49)
2025.07.23 09:11:57 ERROR 	at spray.revolver.RevolverPlugin$.$anonfun$settings$23(RevolverPlugin.scala:105)
2025.07.23 09:11:57 ERROR 	at sbt.Project$.setProject(Project.scala:501)
2025.07.23 09:11:57 ERROR 	at sbt.BuiltinCommands$.doLoadProject(Main.scala:968)
2025.07.23 09:11:57 ERROR 	at sbt.BuiltinCommands$.$anonfun$loadProjectImpl$2(Main.scala:906)
2025.07.23 09:11:57 ERROR 	at sbt.Command$.$anonfun$applyEffect$4(Command.scala:150)
2025.07.23 09:11:57 ERROR 	at sbt.Command$.$anonfun$applyEffect$2(Command.scala:145)
2025.07.23 09:11:57 ERROR 	at sbt.Command$.process(Command.scala:189)
2025.07.23 09:11:57 ERROR 	at sbt.MainLoop$.$anonfun$processCommand$5(MainLoop.scala:245)
2025.07.23 09:11:57 ERROR 	at scala.Option.getOrElse(Option.scala:189)
2025.07.23 09:11:57 ERROR 	at sbt.MainLoop$.process$1(MainLoop.scala:245)
2025.07.23 09:11:57 ERROR 	at sbt.MainLoop$.processCommand(MainLoop.scala:278)
2025.07.23 09:11:57 ERROR 	at sbt.MainLoop$.$anonfun$next$5(MainLoop.scala:163)
2025.07.23 09:11:57 ERROR 	at sbt.State$StateOpsImpl$.runCmd$1(State.scala:289)
2025.07.23 09:11:57 ERROR 	at sbt.State$StateOpsImpl$.process$extension(State.scala:325)
2025.07.23 09:11:57 ERROR 	at sbt.MainLoop$.$anonfun$next$4(MainLoop.scala:163)
2025.07.23 09:11:57 ERROR 	at sbt.internal.util.ErrorHandling$.wideConvert(ErrorHandling.scala:23)
2025.07.23 09:11:57 ERROR 	at sbt.MainLoop$.next(MainLoop.scala:163)
2025.07.23 09:11:57 ERROR 	at sbt.MainLoop$.run(MainLoop.scala:144)
2025.07.23 09:11:57 ERROR 	at sbt.MainLoop$.$anonfun$runWithNewLog$1(MainLoop.scala:119)
2025.07.23 09:11:57 ERROR 	at sbt.io.Using.apply(Using.scala:27)
2025.07.23 09:11:57 ERROR 	at sbt.MainLoop$.runWithNewLog(MainLoop.scala:112)
2025.07.23 09:11:57 ERROR 	at sbt.MainLoop$.runAndClearLast(MainLoop.scala:66)
2025.07.23 09:11:57 ERROR 	at sbt.MainLoop$.runLoggedLoop(MainLoop.scala:51)
2025.07.23 09:11:57 ERROR 	at sbt.MainLoop$.runLogged(MainLoop.scala:42)
2025.07.23 09:11:57 ERROR 	at sbt.StandardMain$.runManaged(Main.scala:215)
2025.07.23 09:11:57 ERROR 	at sbt.xMain$.$anonfun$run$11(Main.scala:133)
2025.07.23 09:11:57 ERROR 	at scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)
2025.07.23 09:11:57 ERROR 	at scala.Console$.withIn(Console.scala:230)
2025.07.23 09:11:57 ERROR 	at sbt.internal.util.Terminal$.withIn(Terminal.scala:569)
2025.07.23 09:11:57 ERROR 	at sbt.internal.util.Terminal$.$anonfun$withStreams$1(Terminal.scala:350)
2025.07.23 09:11:57 ERROR 	at scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)
2025.07.23 09:11:57 ERROR 	at scala.Console$.withOut(Console.scala:167)
2025.07.23 09:11:57 ERROR 	at sbt.internal.util.Terminal$.$anonfun$withOut$2(Terminal.scala:559)
2025.07.23 09:11:57 ERROR 	at scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)
2025.07.23 09:11:57 ERROR 	at scala.Console$.withErr(Console.scala:196)
2025.07.23 09:11:57 ERROR 	at sbt.internal.util.Terminal$.withOut(Terminal.scala:559)
2025.07.23 09:11:57 ERROR 	at sbt.internal.util.Terminal$.withStreams(Terminal.scala:350)
2025.07.23 09:11:57 ERROR 	at sbt.xMain$.withStreams$1(Main.scala:87)
2025.07.23 09:11:57 ERROR 	at sbt.xMain$.run(Main.scala:121)
2025.07.23 09:11:57 ERROR 	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
2025.07.23 09:11:57 ERROR 	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
2025.07.23 09:11:57 ERROR 	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
2025.07.23 09:11:57 ERROR 	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
2025.07.23 09:11:57 ERROR 	at sbt.internal.XMainConfiguration.run(XMainConfiguration.java:57)
2025.07.23 09:11:57 ERROR 	at sbt.xMain.run(Main.scala:46)
2025.07.23 09:11:57 ERROR 	at xsbt.boot.Launch$.$anonfun$run$1(Launch.scala:149)
2025.07.23 09:11:57 ERROR 	at xsbt.boot.Launch$.withContextLoader(Launch.scala:176)
2025.07.23 09:11:57 ERROR 	at xsbt.boot.Launch$.run(Launch.scala:149)
2025.07.23 09:11:57 ERROR 	at xsbt.boot.Launch$.$anonfun$apply$1(Launch.scala:44)
2025.07.23 09:11:57 ERROR 	at xsbt.boot.Launch$.launch(Launch.scala:159)
2025.07.23 09:11:57 ERROR 	at xsbt.boot.Launch$.apply(Launch.scala:44)
2025.07.23 09:11:57 ERROR 	at xsbt.boot.Launch$.apply(Launch.scala:21)
2025.07.23 09:11:57 ERROR 	at xsbt.boot.Boot$.runImpl(Boot.scala:78)
2025.07.23 09:11:57 ERROR 	at xsbt.boot.Boot$.run(Boot.scala:73)
2025.07.23 09:11:57 ERROR 	at xsbt.boot.Boot$.main(Boot.scala:21)
2025.07.23 09:11:57 ERROR 	at xsbt.boot.Boot.main(Boot.scala)
2025.07.23 09:11:57 INFO  [success] Generated .bloop/root-test.json
2025.07.23 09:11:57 INFO  [success] Generated .bloop/root.json
2025.07.23 09:11:57 INFO  [success] Total time: 0 s, completed Jul 23, 2025, 9:11:57 AM
2025.07.23 09:11:58 INFO  time: ran 'sbt bloopInstall' in 10s
2025.07.23 09:11:58 INFO  Attempting to connect to the build server...
2025.07.23 09:11:58 INFO  No running Bloop server found, starting one.
2025.07.23 09:11:58 INFO  Starting compilation server
2025.07.23 09:11:59 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /home/<USER>/projects/match-manager/.metals/bsp.trace.json or /home/<USER>/.cache/metals/bsp.trace.json
2025.07.23 09:12:03 INFO  Attempting to connect to the build server...
2025.07.23 09:12:03 INFO  Found a Bloop server running
2025.07.23 09:12:03 INFO  Attempting to connect to the build server...
2025.07.23 09:12:03 INFO  Found a Bloop server running
2025.07.23 09:12:03 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /home/<USER>/projects/match-manager/project/project/.metals/bsp.trace.json or /home/<USER>/.cache/metals/bsp.trace.json
2025.07.23 09:12:03 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /home/<USER>/projects/match-manager/project/.metals/bsp.trace.json or /home/<USER>/.cache/metals/bsp.trace.json
2025.07.23 09:12:05 INFO  time: Connected to build server in 7.67s
2025.07.23 09:12:05 INFO  Connected to Build server: Bloop v2.0.10
2025.07.23 09:12:06 INFO  time: Imported build in 0.11s
2025.07.23 09:12:06 WARN  2.12.15 is no longer supported in the current Metals versions, using the last known supported version 1.3.3
2025.07.23 09:12:06 WARN  2.13.9 is no longer supported in the current Metals versions, using the last known supported version 1.3.3
2025.07.23 09:12:08 INFO  compiling root (21 scala sources)
2025.07.23 09:12:18 INFO  time: indexed workspace in 12s
2025.07.23 09:12:18 INFO  Deduplicating compilation of root from bsp client 'Metals 1.6.0' (since 19.088s)
2025.07.23 09:12:18 INFO  compiling root (21 scala sources)
2025.07.23 09:12:24 INFO  compiling root-test (4 scala sources)
2025.07.23 09:12:24 INFO  time: compiled root in 6.26s
2025.07.23 09:12:26 INFO  time: compiled root-test in 1.71s
2025.07.23 09:17:03 INFO  compiling root (1 scala source)
2025.07.23 09:17:03 INFO  time: compiled root in 0.54s
2025.07.29 12:54:45 INFO  Started: Metals version 1.6.0+86-83e295cb-SNAPSHOT in folders '/home/<USER>/projects/match-manager' for client Visual Studio Code - Insiders 1.103.0-insider.
2025.07.29 12:54:45 INFO  tracing is disabled for protocol mcp, to enable tracing of incoming and outgoing JSON messages create an empty file at /home/<USER>/projects/match-manager/.metals/mcp.trace.json or /home/<USER>/.cache/metals/mcp.trace.json
2025.07.29 12:54:45 INFO  Attempting to connect to the build server...
2025.07.29 12:54:45 INFO  no build target found for /home/<USER>/projects/match-manager/src/main/scala/nl/dpgmedia/matchmanager/Database.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.07.29 12:54:45 INFO  Found a Bloop server running
Jul 29, 2025 12:54:46 PM io.undertow.Undertow start
INFO: starting server: Undertow - 2.3.12.Final
Jul 29, 2025 12:54:46 PM org.xnio.Xnio <clinit>
INFO: XNIO version 3.8.16.Final
2025.07.29 12:54:46 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /home/<USER>/projects/match-manager/.metals/bsp.trace.json or /home/<USER>/.cache/metals/bsp.trace.json
Jul 29, 2025 12:54:46 PM org.xnio.nio.NioXnio <clinit>
INFO: XNIO NIO Implementation Version 3.8.16.Final
Jul 29, 2025 12:54:46 PM org.jboss.threads.Version <clinit>
INFO: JBoss Threads version 3.5.0.Final
2025.07.29 12:54:45 INFO  Metals MCP server started on port: 45211.
2025.07.29 12:54:47 INFO  restarted 1 presentation compiler
2025.07.29 12:54:48 INFO  Attempting to connect to the build server...
2025.07.29 12:54:48 INFO  Found a Bloop server running
2025.07.29 12:54:48 INFO  Attempting to connect to the build server...
2025.07.29 12:54:48 INFO  Found a Bloop server running
2025.07.29 12:54:48 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /home/<USER>/projects/match-manager/project/project/.metals/bsp.trace.json or /home/<USER>/.cache/metals/bsp.trace.json
2025.07.29 12:54:48 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /home/<USER>/projects/match-manager/project/.metals/bsp.trace.json or /home/<USER>/.cache/metals/bsp.trace.json
2025.07.29 12:54:50 INFO  time: Connected to build server in 4.35s
2025.07.29 12:54:50 INFO  Connected to Build server: Bloop v2.0.10
2025.07.29 12:54:50 WARN  2.12.15 is no longer supported in the current Metals versions, using the last known supported version 1.3.3
2025.07.29 12:54:50 WARN  2.13.9 is no longer supported in the current Metals versions, using the last known supported version 1.3.3
2025.07.29 12:54:52 INFO  time: indexed workspace in 2.44s
2025.07.29 12:54:53 INFO  compiling root (21 scala sources)
2025.07.29 12:55:01 INFO  compiling root-test (4 scala sources)
2025.07.29 12:55:01 INFO  time: compiled root in 8.63s
2025.07.29 12:55:03 INFO  time: compiled root-test in 1.11s
2025.07.29 13:04:32 INFO  compiling root (1 scala source)
2025.07.29 13:04:32 INFO  time: compiled root in 0.18s
2025.07.29 13:04:59 INFO  time: compiled root in 21ms
Jul 29, 2025 1:06:34 PM org.eclipse.lsp4j.jsonrpc.RemoteEndpoint handleNotification
WARNING: Notification threw an exception: {
  "jsonrpc": "2.0",
  "method": "metals/didFocusTextDocument",
  "params": [
    "vscode-chat-code-block://fd2949f3-a326-49af-bf3d-4b589e34ead3/response_c46c365c-67d3-4781-8a0e-b879455a5fec/0#%7B%22references%22%3A%5B%5D%7D"
  ]
}
java.nio.file.FileSystemNotFoundException: Provider "vscode-chat-code-block" not installed
	at java.base/java.nio.file.Path.of(Path.java:212)
	at java.base/java.nio.file.Paths.get(Paths.java:98)
	at scala.meta.internal.mtags.MtagsEnrichments$XtensionURIMtags.toAbsolutePath(MtagsEnrichments.scala:130)
	at scala.meta.internal.mtags.MtagsEnrichments$XtensionStringMtags.toAbsolutePath(MtagsEnrichments.scala:190)
	at scala.meta.internal.metals.MetalsEnrichments$XtensionString.toAbsolutePath(MetalsEnrichments.scala:787)
	at scala.meta.internal.metals.MetalsEnrichments$XtensionString.toAbsolutePath(MetalsEnrichments.scala:784)
	at scala.meta.internal.metals.WorkspaceLspService.didFocus(WorkspaceLspService.scala:786)
	at scala.meta.metals.lsp.DelegatingScalaService.didFocus(DelegatingScalaService.scala:42)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint.lambda$recursiveFindRpcMethods$0(GenericEndpoint.java:65)
	at org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint.notify(GenericEndpoint.java:160)
	at org.eclipse.lsp4j.jsonrpc.RemoteEndpoint.handleNotification(RemoteEndpoint.java:231)
	at org.eclipse.lsp4j.jsonrpc.RemoteEndpoint.consume(RemoteEndpoint.java:198)
	at org.eclipse.lsp4j.jsonrpc.json.StreamMessageProducer.handleMessage(StreamMessageProducer.java:185)
	at org.eclipse.lsp4j.jsonrpc.json.StreamMessageProducer.listen(StreamMessageProducer.java:97)
	at org.eclipse.lsp4j.jsonrpc.json.ConcurrentMessageProcessor.run(ConcurrentMessageProcessor.java:114)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)

Jul 29, 2025 1:06:36 PM org.eclipse.lsp4j.jsonrpc.RemoteEndpoint handleNotification
WARNING: Notification threw an exception: {
  "jsonrpc": "2.0",
  "method": "metals/didFocusTextDocument",
  "params": [
    "vscode-chat-code-block://fd2949f3-a326-49af-bf3d-4b589e34ead3/response_c46c365c-67d3-4781-8a0e-b879455a5fec/0#%7B%22references%22%3A%5B%5D%7D"
  ]
}
java.nio.file.FileSystemNotFoundException: Provider "vscode-chat-code-block" not installed
	at java.base/java.nio.file.Path.of(Path.java:212)
	at java.base/java.nio.file.Paths.get(Paths.java:98)
	at scala.meta.internal.mtags.MtagsEnrichments$XtensionURIMtags.toAbsolutePath(MtagsEnrichments.scala:130)
	at scala.meta.internal.mtags.MtagsEnrichments$XtensionStringMtags.toAbsolutePath(MtagsEnrichments.scala:190)
	at scala.meta.internal.metals.MetalsEnrichments$XtensionString.toAbsolutePath(MetalsEnrichments.scala:787)
	at scala.meta.internal.metals.MetalsEnrichments$XtensionString.toAbsolutePath(MetalsEnrichments.scala:784)
	at scala.meta.internal.metals.WorkspaceLspService.didFocus(WorkspaceLspService.scala:786)
	at scala.meta.metals.lsp.DelegatingScalaService.didFocus(DelegatingScalaService.scala:42)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint.lambda$recursiveFindRpcMethods$0(GenericEndpoint.java:65)
	at org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint.notify(GenericEndpoint.java:160)
	at org.eclipse.lsp4j.jsonrpc.RemoteEndpoint.handleNotification(RemoteEndpoint.java:231)
	at org.eclipse.lsp4j.jsonrpc.RemoteEndpoint.consume(RemoteEndpoint.java:198)
	at org.eclipse.lsp4j.jsonrpc.json.StreamMessageProducer.handleMessage(StreamMessageProducer.java:185)
	at org.eclipse.lsp4j.jsonrpc.json.StreamMessageProducer.listen(StreamMessageProducer.java:97)
	at org.eclipse.lsp4j.jsonrpc.json.ConcurrentMessageProcessor.run(ConcurrentMessageProcessor.java:114)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)

Jul 29, 2025 1:08:01 PM org.eclipse.lsp4j.jsonrpc.RemoteEndpoint handleNotification
WARNING: Notification threw an exception: {
  "jsonrpc": "2.0",
  "method": "metals/didFocusTextDocument",
  "params": [
    "vscode-chat-code-block://fd2949f3-a326-49af-bf3d-4b589e34ead3/response_138fa478-6ea3-430f-87c3-8aef1dae6040/0#%7B%22references%22%3A%5B%5D%7D"
  ]
}
java.nio.file.FileSystemNotFoundException: Provider "vscode-chat-code-block" not installed
	at java.base/java.nio.file.Path.of(Path.java:212)
	at java.base/java.nio.file.Paths.get(Paths.java:98)
	at scala.meta.internal.mtags.MtagsEnrichments$XtensionURIMtags.toAbsolutePath(MtagsEnrichments.scala:130)
	at scala.meta.internal.mtags.MtagsEnrichments$XtensionStringMtags.toAbsolutePath(MtagsEnrichments.scala:190)
	at scala.meta.internal.metals.MetalsEnrichments$XtensionString.toAbsolutePath(MetalsEnrichments.scala:787)
	at scala.meta.internal.metals.MetalsEnrichments$XtensionString.toAbsolutePath(MetalsEnrichments.scala:784)
	at scala.meta.internal.metals.WorkspaceLspService.didFocus(WorkspaceLspService.scala:786)
	at scala.meta.metals.lsp.DelegatingScalaService.didFocus(DelegatingScalaService.scala:42)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint.lambda$recursiveFindRpcMethods$0(GenericEndpoint.java:65)
	at org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint.notify(GenericEndpoint.java:160)
	at org.eclipse.lsp4j.jsonrpc.RemoteEndpoint.handleNotification(RemoteEndpoint.java:231)
	at org.eclipse.lsp4j.jsonrpc.RemoteEndpoint.consume(RemoteEndpoint.java:198)
	at org.eclipse.lsp4j.jsonrpc.json.StreamMessageProducer.handleMessage(StreamMessageProducer.java:185)
	at org.eclipse.lsp4j.jsonrpc.json.StreamMessageProducer.listen(StreamMessageProducer.java:97)
	at org.eclipse.lsp4j.jsonrpc.json.ConcurrentMessageProcessor.run(ConcurrentMessageProcessor.java:114)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)

Jul 29, 2025 1:08:37 PM org.eclipse.lsp4j.jsonrpc.RemoteEndpoint handleNotification
WARNING: Notification threw an exception: {
  "jsonrpc": "2.0",
  "method": "metals/didFocusTextDocument",
  "params": [
    "vscode-chat-code-block://fd2949f3-a326-49af-bf3d-4b589e34ead3/response_138fa478-6ea3-430f-87c3-8aef1dae6040/3#%7B%22references%22%3A%5B%5D%7D"
  ]
}
java.nio.file.FileSystemNotFoundException: Provider "vscode-chat-code-block" not installed
	at java.base/java.nio.file.Path.of(Path.java:212)
	at java.base/java.nio.file.Paths.get(Paths.java:98)
	at scala.meta.internal.mtags.MtagsEnrichments$XtensionURIMtags.toAbsolutePath(MtagsEnrichments.scala:130)
	at scala.meta.internal.mtags.MtagsEnrichments$XtensionStringMtags.toAbsolutePath(MtagsEnrichments.scala:190)
	at scala.meta.internal.metals.MetalsEnrichments$XtensionString.toAbsolutePath(MetalsEnrichments.scala:787)
	at scala.meta.internal.metals.MetalsEnrichments$XtensionString.toAbsolutePath(MetalsEnrichments.scala:784)
	at scala.meta.internal.metals.WorkspaceLspService.didFocus(WorkspaceLspService.scala:786)
	at scala.meta.metals.lsp.DelegatingScalaService.didFocus(DelegatingScalaService.scala:42)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint.lambda$recursiveFindRpcMethods$0(GenericEndpoint.java:65)
	at org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint.notify(GenericEndpoint.java:160)
	at org.eclipse.lsp4j.jsonrpc.RemoteEndpoint.handleNotification(RemoteEndpoint.java:231)
	at org.eclipse.lsp4j.jsonrpc.RemoteEndpoint.consume(RemoteEndpoint.java:198)
	at org.eclipse.lsp4j.jsonrpc.json.StreamMessageProducer.handleMessage(StreamMessageProducer.java:185)
	at org.eclipse.lsp4j.jsonrpc.json.StreamMessageProducer.listen(StreamMessageProducer.java:97)
	at org.eclipse.lsp4j.jsonrpc.json.ConcurrentMessageProcessor.run(ConcurrentMessageProcessor.java:114)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025.07.29 13:09:49 INFO  compiling root (1 scala source)
2025.07.29 13:09:49 INFO  time: compiled root in 0.9s
2025.07.29 13:10:07 INFO  compiling root (1 scala source)
2025.07.29 13:10:07 INFO  time: compiled root in 0.7s
Jul 29, 2025 1:10:16 PM org.eclipse.lsp4j.jsonrpc.RemoteEndpoint handleNotification
WARNING: Notification threw an exception: {
  "jsonrpc": "2.0",
  "method": "metals/didFocusTextDocument",
  "params": [
    "chat-editing-snapshot-text-model:/home/<USER>/projects/match-manager/src/main/scala/nl/dpgmedia/matchmanager/Database.scala?%7B%22sessionId%22%3A%22fd2949f3-a326-49af-bf3d-4b589e34ead3%22%2C%22requestId%22%3A%22request_7711efa2-c2e9-44be-ac86-7808d6805372%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D"
  ]
}
java.nio.file.FileSystemNotFoundException: Provider "chat-editing-snapshot-text-model" not installed
	at java.base/java.nio.file.Path.of(Path.java:212)
	at java.base/java.nio.file.Paths.get(Paths.java:98)
	at scala.meta.internal.mtags.MtagsEnrichments$XtensionURIMtags.toAbsolutePath(MtagsEnrichments.scala:130)
	at scala.meta.internal.mtags.MtagsEnrichments$XtensionStringMtags.toAbsolutePath(MtagsEnrichments.scala:190)
	at scala.meta.internal.metals.MetalsEnrichments$XtensionString.toAbsolutePath(MetalsEnrichments.scala:787)
	at scala.meta.internal.metals.MetalsEnrichments$XtensionString.toAbsolutePath(MetalsEnrichments.scala:784)
	at scala.meta.internal.metals.WorkspaceLspService.didFocus(WorkspaceLspService.scala:786)
	at scala.meta.metals.lsp.DelegatingScalaService.didFocus(DelegatingScalaService.scala:42)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint.lambda$recursiveFindRpcMethods$0(GenericEndpoint.java:65)
	at org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint.notify(GenericEndpoint.java:160)
	at org.eclipse.lsp4j.jsonrpc.RemoteEndpoint.handleNotification(RemoteEndpoint.java:231)
	at org.eclipse.lsp4j.jsonrpc.RemoteEndpoint.consume(RemoteEndpoint.java:198)
	at org.eclipse.lsp4j.jsonrpc.json.StreamMessageProducer.handleMessage(StreamMessageProducer.java:185)
	at org.eclipse.lsp4j.jsonrpc.json.StreamMessageProducer.listen(StreamMessageProducer.java:97)
	at org.eclipse.lsp4j.jsonrpc.json.ConcurrentMessageProcessor.run(ConcurrentMessageProcessor.java:114)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025.07.29 13:10:25 INFO  compiling root (1 scala source)
2025.07.29 13:10:25 INFO  time: compiled root in 0.82s
2025.07.29 13:11:10 INFO  compiling root (2 scala sources)
2025.07.29 13:11:10 WARN  Could not load snapshot text for /home/<USER>/projects/match-manager/src/main/scala/nl/dpgmedia/matchmanager/Database.scala
2025.07.29 13:11:10 INFO  time: compiled root in 0.96s
2025.07.29 13:13:26 INFO  compiling root (1 scala source)
2025.07.29 13:13:26 INFO  time: compiled root in 0.41s
2025.07.29 13:14:57 INFO  compiling root (1 scala source)
2025.07.29 13:14:57 INFO  time: compiled root in 0.81s
2025.07.29 13:15:20 INFO  compiling root (1 scala source)
2025.07.29 13:15:20 INFO  time: compiled root in 0.53s
2025.07.29 13:18:02 INFO  compiling root (1 scala source)
2025.07.29 13:18:02 INFO  time: compiled root in 0.47s
2025.07.29 13:18:02 INFO  compiling root (1 scala source)
2025.07.29 13:18:02 INFO  time: compiled root in 61ms
2025.07.29 13:18:11 INFO  compiling root (2 scala sources)
2025.07.29 13:18:11 INFO  time: compiled root in 0.71s
Jul 29, 2025 1:22:26 PM org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint notify
INFO: Unsupported notification method: $/setTrace
Jul 29, 2025 1:24:46 PM org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint notify
INFO: Unsupported notification method: $/setTrace
2025.07.29 13:32:33 INFO  compiling root (2 scala sources)
2025.07.29 13:32:33 INFO  time: compiled root in 0.83s
2025.07.29 13:32:47 INFO  compiling root (1 scala source)
2025.07.29 13:32:47 INFO  time: compiled root in 0.76s
2025.07.29 13:34:02 INFO  compiling root (1 scala source)
2025.07.29 13:34:02 INFO  time: compiled root in 0.45s
2025.07.29 13:34:39 INFO  compiling root (1 scala source)
2025.07.29 13:34:39 INFO  time: compiled root in 0.77s
2025.07.29 13:34:57 INFO  compiling root (1 scala source)
2025.07.29 13:34:57 INFO  time: compiled root in 0.48s
2025.07.29 13:38:28 INFO  compiling root (1 scala source)
2025.07.29 13:38:28 INFO  time: compiled root in 0.51s
2025.07.29 13:38:59 INFO  compiling root (1 scala source)
2025.07.29 13:38:59 INFO  time: compiled root in 0.66s
2025.07.29 13:43:16 INFO  running '/usr/bin/sbt -Dbloop.export-jar-classifiers=sources bloopInstall'
2025.07.29 13:43:18 INFO  [info] welcome to sbt 1.6.1 (Eclipse Adoptium Java 17.0.15)
2025.07.29 13:43:18 INFO  [info] loading settings for project match-manager-build-build-build from metals.sbt ...
2025.07.29 13:43:19 INFO  [info] loading project definition from /home/<USER>/projects/match-manager/project/project/project
2025.07.29 13:43:19 INFO  [info] loading settings for project match-manager-build-build from metals.sbt ...
2025.07.29 13:43:19 INFO  [info] loading project definition from /home/<USER>/projects/match-manager/project/project
2025.07.29 13:43:21 INFO  [success] Generated .bloop/match-manager-build-build.json
2025.07.29 13:43:21 INFO  [success] Total time: 1 s, completed Jul 29, 2025, 1:43:21 PM
2025.07.29 13:43:21 INFO  [info] loading settings for project match-manager-build from metals.sbt,plugins.sbt ...
2025.07.29 13:43:21 INFO  [info] loading project definition from /home/<USER>/projects/match-manager/project
2025.07.29 13:43:21 INFO  [success] Generated .bloop/match-manager-build.json
2025.07.29 13:43:21 INFO  [success] Total time: 0 s, completed Jul 29, 2025, 1:43:22 PM
2025.07.29 13:43:23 INFO  [info] loading settings for project root from build.sbt,deploy.sbt,docker.sbt ...
2025.07.29 13:43:23 INFO  [info] set current project to match-manager (in build file:/home/<USER>/projects/match-manager/)
2025.07.29 13:43:23 INFO  [info] .env detected (fileName=.env). About to configure JVM System Environment with new map: Map(DATABASE_USER -> (your database user for the test environment), DATABASE_CONNECTION_STRING -> **************************************************************************************************************, PRIVATE_KEY_CONTENT -> -----BEGIN PRIVATE KEY-----nMII...n-----END PRIVATE KEY-----)
2025.07.29 13:43:23 INFO  [info] Configured .env environment
2025.07.29 13:43:23 ERROR java.lang.reflect.InaccessibleObjectException: Unable to make field private static final java.util.HashMap java.lang.ProcessEnvironment.theEnvironment accessible: module java.base does not "opens java.lang" to unnamed module @3c592c0c
2025.07.29 13:43:23 ERROR 	at java.base/java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:354)
2025.07.29 13:43:23 ERROR 	at java.base/java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:297)
2025.07.29 13:43:23 ERROR 	at java.base/java.lang.reflect.Field.checkCanSetAccessible(Field.java:178)
2025.07.29 13:43:23 ERROR 	at java.base/java.lang.reflect.Field.setAccessible(Field.java:172)
2025.07.29 13:43:23 ERROR 	at au.com.onegeek.sbtdotenv.DirtyEnvironmentHack$.$anonfun$setEnv$1(DirtyEnvironmentHack.scala:42)
2025.07.29 13:43:23 ERROR 	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)
2025.07.29 13:43:23 ERROR 	at scala.util.Try$.apply(Try.scala:213)
2025.07.29 13:43:23 ERROR 	at au.com.onegeek.sbtdotenv.DirtyEnvironmentHack$.setEnv(DirtyEnvironmentHack.scala:38)
2025.07.29 13:43:23 ERROR 	at au.com.onegeek.sbtdotenv.SbtDotenv$.applyEnvironment(SbtDotenv.scala:90)
2025.07.29 13:43:23 ERROR 	at au.com.onegeek.sbtdotenv.SbtDotenv$.$anonfun$configureEnvironment$2(SbtDotenv.scala:75)
2025.07.29 13:43:23 ERROR 	at scala.Option.fold(Option.scala:251)
2025.07.29 13:43:23 ERROR 	at au.com.onegeek.sbtdotenv.SbtDotenv$.configureEnvironment(SbtDotenv.scala:75)
2025.07.29 13:43:23 ERROR 	at au.com.onegeek.sbtdotenv.SbtDotenv$autoImport$.$anonfun$dotEnv$1(SbtDotenv.scala:43)
2025.07.29 13:43:23 ERROR 	at scala.Function1.$anonfun$compose$1(Function1.scala:49)
2025.07.29 13:43:23 ERROR 	at spray.revolver.RevolverPlugin$.$anonfun$settings$23(RevolverPlugin.scala:105)
2025.07.29 13:43:23 ERROR 	at sbt.Project$.setProject(Project.scala:501)
2025.07.29 13:43:23 ERROR 	at sbt.BuiltinCommands$.doLoadProject(Main.scala:968)
2025.07.29 13:43:23 ERROR 	at sbt.BuiltinCommands$.$anonfun$loadProjectImpl$2(Main.scala:906)
2025.07.29 13:43:23 ERROR 	at sbt.Command$.$anonfun$applyEffect$4(Command.scala:150)
2025.07.29 13:43:23 ERROR 	at sbt.Command$.$anonfun$applyEffect$2(Command.scala:145)
2025.07.29 13:43:23 ERROR 	at sbt.Command$.process(Command.scala:189)
2025.07.29 13:43:23 ERROR 	at sbt.MainLoop$.$anonfun$processCommand$5(MainLoop.scala:245)
2025.07.29 13:43:23 ERROR 	at scala.Option.getOrElse(Option.scala:189)
2025.07.29 13:43:23 ERROR 	at sbt.MainLoop$.process$1(MainLoop.scala:245)
2025.07.29 13:43:23 ERROR 	at sbt.MainLoop$.processCommand(MainLoop.scala:278)
2025.07.29 13:43:23 ERROR 	at sbt.MainLoop$.$anonfun$next$5(MainLoop.scala:163)
2025.07.29 13:43:23 ERROR 	at sbt.State$StateOpsImpl$.runCmd$1(State.scala:289)
2025.07.29 13:43:23 ERROR 	at sbt.State$StateOpsImpl$.process$extension(State.scala:325)
2025.07.29 13:43:23 ERROR 	at sbt.MainLoop$.$anonfun$next$4(MainLoop.scala:163)
2025.07.29 13:43:23 ERROR 	at sbt.internal.util.ErrorHandling$.wideConvert(ErrorHandling.scala:23)
2025.07.29 13:43:23 ERROR 	at sbt.MainLoop$.next(MainLoop.scala:163)
2025.07.29 13:43:23 ERROR 	at sbt.MainLoop$.run(MainLoop.scala:144)
2025.07.29 13:43:23 ERROR 	at sbt.MainLoop$.$anonfun$runWithNewLog$1(MainLoop.scala:119)
2025.07.29 13:43:23 ERROR 	at sbt.io.Using.apply(Using.scala:27)
2025.07.29 13:43:23 ERROR 	at sbt.MainLoop$.runWithNewLog(MainLoop.scala:112)
2025.07.29 13:43:23 ERROR 	at sbt.MainLoop$.runAndClearLast(MainLoop.scala:66)
2025.07.29 13:43:23 ERROR 	at sbt.MainLoop$.runLoggedLoop(MainLoop.scala:51)
2025.07.29 13:43:23 ERROR 	at sbt.MainLoop$.runLogged(MainLoop.scala:42)
2025.07.29 13:43:23 ERROR 	at sbt.StandardMain$.runManaged(Main.scala:215)
2025.07.29 13:43:23 ERROR 	at sbt.xMain$.$anonfun$run$11(Main.scala:133)
2025.07.29 13:43:23 ERROR 	at scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)
2025.07.29 13:43:23 ERROR 	at scala.Console$.withIn(Console.scala:230)
2025.07.29 13:43:23 ERROR 	at sbt.internal.util.Terminal$.withIn(Terminal.scala:569)
2025.07.29 13:43:23 ERROR 	at sbt.internal.util.Terminal$.$anonfun$withStreams$1(Terminal.scala:350)
2025.07.29 13:43:23 ERROR 	at scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)
2025.07.29 13:43:23 ERROR 	at scala.Console$.withOut(Console.scala:167)
2025.07.29 13:43:23 ERROR 	at sbt.internal.util.Terminal$.$anonfun$withOut$2(Terminal.scala:559)
2025.07.29 13:43:23 ERROR 	at scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)
2025.07.29 13:43:23 ERROR 	at scala.Console$.withErr(Console.scala:196)
2025.07.29 13:43:23 ERROR 	at sbt.internal.util.Terminal$.withOut(Terminal.scala:559)
2025.07.29 13:43:23 ERROR 	at sbt.internal.util.Terminal$.withStreams(Terminal.scala:350)
2025.07.29 13:43:23 ERROR 	at sbt.xMain$.withStreams$1(Main.scala:87)
2025.07.29 13:43:23 ERROR 	at sbt.xMain$.run(Main.scala:121)
2025.07.29 13:43:23 ERROR 	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
2025.07.29 13:43:23 ERROR 	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
2025.07.29 13:43:23 ERROR 	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
2025.07.29 13:43:23 ERROR 	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
2025.07.29 13:43:23 ERROR 	at sbt.internal.XMainConfiguration.run(XMainConfiguration.java:57)
2025.07.29 13:43:23 ERROR 	at sbt.xMain.run(Main.scala:46)
2025.07.29 13:43:23 ERROR 	at xsbt.boot.Launch$.$anonfun$run$1(Launch.scala:149)
2025.07.29 13:43:23 ERROR 	at xsbt.boot.Launch$.withContextLoader(Launch.scala:176)
2025.07.29 13:43:23 ERROR 	at xsbt.boot.Launch$.run(Launch.scala:149)
2025.07.29 13:43:23 ERROR 	at xsbt.boot.Launch$.$anonfun$apply$1(Launch.scala:44)
2025.07.29 13:43:23 ERROR 	at xsbt.boot.Launch$.launch(Launch.scala:159)
2025.07.29 13:43:23 ERROR 	at xsbt.boot.Launch$.apply(Launch.scala:44)
2025.07.29 13:43:23 ERROR 	at xsbt.boot.Launch$.apply(Launch.scala:21)
2025.07.29 13:43:23 ERROR 	at xsbt.boot.Boot$.runImpl(Boot.scala:78)
2025.07.29 13:43:23 ERROR 	at xsbt.boot.Boot$.run(Boot.scala:73)
2025.07.29 13:43:23 ERROR 	at xsbt.boot.Boot$.main(Boot.scala:21)
2025.07.29 13:43:23 ERROR 	at xsbt.boot.Boot.main(Boot.scala)
2025.07.29 13:43:26 INFO  [success] Generated .bloop/root-test.json
2025.07.29 13:43:26 INFO  [success] Generated .bloop/root.json
2025.07.29 13:43:26 INFO  [success] Total time: 3 s, completed Jul 29, 2025, 1:43:26 PM
2025.07.29 13:43:26 INFO  time: ran 'sbt bloopInstall' in 9.94s
2025.07.29 13:43:26 INFO  Disconnecting from Bloop session...
2025.07.29 13:43:26 INFO  Shut down connection with build server.
2025.07.29 13:43:26 INFO  Shut down connection with build server.
2025.07.29 13:43:26 INFO  Shut down connection with build server.
2025.07.29 13:43:26 INFO  Attempting to connect to the build server...
2025.07.29 13:43:26 INFO  Found a Bloop server running
2025.07.29 13:43:27 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /home/<USER>/projects/match-manager/.metals/bsp.trace.json or /home/<USER>/.cache/metals/bsp.trace.json
2025.07.29 13:43:29 INFO  Attempting to connect to the build server...
2025.07.29 13:43:29 INFO  Found a Bloop server running
2025.07.29 13:43:29 INFO  Attempting to connect to the build server...
2025.07.29 13:43:29 INFO  Found a Bloop server running
2025.07.29 13:43:30 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /home/<USER>/projects/match-manager/project/.metals/bsp.trace.json or /home/<USER>/.cache/metals/bsp.trace.json
2025.07.29 13:43:29 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /home/<USER>/projects/match-manager/project/project/.metals/bsp.trace.json or /home/<USER>/.cache/metals/bsp.trace.json
2025.07.29 13:43:32 INFO  time: Connected to build server in 5.28s
2025.07.29 13:43:32 INFO  Connected to Build server: Bloop v2.0.10
2025.07.29 13:43:35 INFO  time: Imported build in 3.52s
2025.07.29 13:43:40 INFO  time: indexed workspace in 4.46s
2025.07.29 13:43:40 INFO  compiling root (21 scala sources)
2025.07.29 13:43:45 INFO  time: compiled root in 4.86s
2025.07.29 13:43:45 INFO  compiling root-test (4 scala sources)
2025.07.29 13:43:45 INFO  time: compiled root-test in 0.79s
2025.07.29 13:43:47 INFO  compiling root (1 scala source)
2025.07.29 13:43:47 INFO  time: compiled root in 0.6s
2025.07.29 13:43:56 INFO  compiling root (1 scala source)
2025.07.29 13:43:57 WARN  Could not load snapshot text for /home/<USER>/projects/match-manager/src/main/scala/nl/dpgmedia/matchmanager/Database.scala
Jul 29, 2025 1:43:57 PM org.eclipse.lsp4j.jsonrpc.RemoteEndpoint handleCancellation
WARNING: Unmatched cancel notification for request id 481
2025.07.29 13:43:56 INFO  time: compiled root in 0.42s
2025.07.29 13:45:13 INFO  compiling root (1 scala source)
2025.07.29 13:45:13 INFO  time: compiled root in 0.55s
2025.07.29 13:45:32 INFO  scalafmt: excluded /home/<USER>/projects/match-manager/build.sbt (to format this file, update `project.excludeFilters` in .scalafmt.conf)
2025.07.29 13:45:32 INFO  compiling root (1 scala source)
2025.07.29 13:45:32 INFO  skipping build import with status 'Installed'
2025.07.29 13:45:32 WARN  Could not load snapshot text for /home/<USER>/projects/match-manager/src/main/scala/nl/dpgmedia/matchmanager/Database.scala
2025.07.29 13:45:32 INFO  time: compiled root in 0.51s
2025.07.29 13:47:45 INFO  compiling root (1 scala source)
2025.07.29 13:47:45 INFO  time: compiled root in 0.59s
2025.07.29 13:50:05 INFO  compiling root (1 scala source)
2025.07.29 13:50:05 INFO  time: compiled root in 0.83s
2025.07.29 13:50:19 INFO  compiling root (1 scala source)
2025.07.29 13:50:20 WARN  Could not load snapshot text for /home/<USER>/projects/match-manager/src/main/scala/nl/dpgmedia/matchmanager/Database.scala
2025.07.29 13:50:19 INFO  time: compiled root in 0.58s
2025.07.29 13:50:46 INFO  compiling root (1 scala source)
2025.07.29 13:50:46 INFO  time: compiled root in 0.46s
2025.07.29 13:50:58 INFO  compiling root (1 scala source)
2025.07.29 13:50:59 WARN  Could not load snapshot text for /home/<USER>/projects/match-manager/src/main/scala/nl/dpgmedia/matchmanager/Database.scala
2025.07.29 13:50:58 INFO  time: compiled root in 0.81s
2025.07.29 13:51:15 INFO  compiling root (1 scala source)
2025.07.29 13:51:15 INFO  time: compiled root in 0.84s
2025.07.29 13:58:12 INFO  compiling root (1 scala source)
2025.07.29 13:58:12 INFO  time: compiled root in 0.69s
2025.07.29 13:58:33 INFO  compiling root (1 scala source)
2025.07.29 13:58:33 WARN  Could not load snapshot text for /home/<USER>/projects/match-manager/src/main/scala/nl/dpgmedia/matchmanager/Database.scala
2025.07.29 13:58:33 INFO  time: compiled root in 0.71s
2025.07.29 13:59:02 INFO  compiling root (1 scala source)
2025.07.29 13:59:02 INFO  time: compiled root in 0.45s
2025.07.29 14:00:38 INFO  compiling root (1 scala source)
2025.07.29 14:00:38 INFO  time: compiled root in 0.88s
2025.07.29 14:00:51 INFO  compiling root (1 scala source)
2025.07.29 14:00:51 WARN  Could not load snapshot text for /home/<USER>/projects/match-manager/src/main/scala/nl/dpgmedia/matchmanager/Database.scala
2025.07.29 14:00:51 INFO  time: compiled root in 0.44s
2025.07.29 14:01:11 INFO  compiling root (1 scala source)
2025.07.29 14:01:11 INFO  time: compiled root in 0.69s
2025.07.29 14:01:34 INFO  compiling root (1 scala source)
2025.07.29 14:01:35 WARN  Could not load snapshot text for /home/<USER>/projects/match-manager/src/main/scala/nl/dpgmedia/matchmanager/Database.scala
2025.07.29 14:01:34 INFO  time: compiled root in 0.8s
2025.07.29 14:01:49 INFO  compiling root (1 scala source)
2025.07.29 14:01:49 INFO  time: compiled root in 0.73s
2025.07.29 14:01:59 INFO  compiling root (1 scala source)
2025.07.29 14:01:59 WARN  Could not load snapshot text for /home/<USER>/projects/match-manager/src/main/scala/nl/dpgmedia/matchmanager/Database.scala
2025.07.29 14:01:59 INFO  time: compiled root in 0.73s
2025.07.29 14:04:29 INFO  running '/usr/bin/sbt -Dbloop.export-jar-classifiers=sources bloopInstall'
2025.07.29 14:04:30 INFO  [info] welcome to sbt 1.6.1 (Eclipse Adoptium Java 17.0.15)
2025.07.29 14:04:30 INFO  [info] loading settings for project match-manager-build-build-build from metals.sbt ...
2025.07.29 14:04:31 INFO  [info] loading project definition from /home/<USER>/projects/match-manager/project/project/project
2025.07.29 14:04:31 INFO  [info] loading settings for project match-manager-build-build from metals.sbt ...
2025.07.29 14:04:31 INFO  [info] loading project definition from /home/<USER>/projects/match-manager/project/project
2025.07.29 14:04:33 INFO  [success] Generated .bloop/match-manager-build-build.json
2025.07.29 14:04:33 INFO  [success] Total time: 1 s, completed Jul 29, 2025, 2:04:33 PM
2025.07.29 14:04:33 INFO  [info] loading settings for project match-manager-build from metals.sbt,plugins.sbt ...
2025.07.29 14:04:33 INFO  [info] loading project definition from /home/<USER>/projects/match-manager/project
2025.07.29 14:04:33 INFO  [success] Generated .bloop/match-manager-build.json
2025.07.29 14:04:33 INFO  [success] Total time: 1 s, completed Jul 29, 2025, 2:04:34 PM
2025.07.29 14:04:34 INFO  [info] loading settings for project root from build.sbt,deploy.sbt,docker.sbt ...
2025.07.29 14:04:34 INFO  [info] set current project to match-manager (in build file:/home/<USER>/projects/match-manager/)
2025.07.29 14:04:34 INFO  [info] .env detected (fileName=.env). About to configure JVM System Environment with new map: Map(DATABASE_USER -> (your database user for the test environment), DATABASE_CONNECTION_STRING -> **************************************************************************************************************, PRIVATE_KEY_CONTENT -> -----BEGIN PRIVATE KEY-----nMII...n-----END PRIVATE KEY-----)
2025.07.29 14:04:34 ERROR java.lang.reflect.InaccessibleObjectException: Unable to make field private static final java.util.HashMap java.lang.ProcessEnvironment.theEnvironment accessible: module java.base does not "opens java.lang" to unnamed module @1bb51492
2025.07.29 14:04:34 ERROR 	at java.base/java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:354)
2025.07.29 14:04:34 ERROR 	at java.base/java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:297)
2025.07.29 14:04:34 ERROR 	at java.base/java.lang.reflect.Field.checkCanSetAccessible(Field.java:178)
2025.07.29 14:04:34 ERROR 	at java.base/java.lang.reflect.Field.setAccessible(Field.java:172)
2025.07.29 14:04:34 ERROR 	at au.com.onegeek.sbtdotenv.DirtyEnvironmentHack$.$anonfun$setEnv$1(DirtyEnvironmentHack.scala:42)
2025.07.29 14:04:34 ERROR 	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)
2025.07.29 14:04:34 ERROR 	at scala.util.Try$.apply(Try.scala:213)
2025.07.29 14:04:34 INFO  [info] Configured .env environment
2025.07.29 14:04:34 ERROR 	at au.com.onegeek.sbtdotenv.DirtyEnvironmentHack$.setEnv(DirtyEnvironmentHack.scala:38)
2025.07.29 14:04:34 ERROR 	at au.com.onegeek.sbtdotenv.SbtDotenv$.applyEnvironment(SbtDotenv.scala:90)
2025.07.29 14:04:34 ERROR 	at au.com.onegeek.sbtdotenv.SbtDotenv$.$anonfun$configureEnvironment$2(SbtDotenv.scala:75)
2025.07.29 14:04:34 ERROR 	at scala.Option.fold(Option.scala:251)
2025.07.29 14:04:34 ERROR 	at au.com.onegeek.sbtdotenv.SbtDotenv$.configureEnvironment(SbtDotenv.scala:75)
2025.07.29 14:04:34 ERROR 	at au.com.onegeek.sbtdotenv.SbtDotenv$autoImport$.$anonfun$dotEnv$1(SbtDotenv.scala:43)
2025.07.29 14:04:34 ERROR 	at scala.Function1.$anonfun$compose$1(Function1.scala:49)
2025.07.29 14:04:34 ERROR 	at spray.revolver.RevolverPlugin$.$anonfun$settings$23(RevolverPlugin.scala:105)
2025.07.29 14:04:34 ERROR 	at sbt.Project$.setProject(Project.scala:501)
2025.07.29 14:04:34 ERROR 	at sbt.BuiltinCommands$.doLoadProject(Main.scala:968)
2025.07.29 14:04:34 ERROR 	at sbt.BuiltinCommands$.$anonfun$loadProjectImpl$2(Main.scala:906)
2025.07.29 14:04:34 ERROR 	at sbt.Command$.$anonfun$applyEffect$4(Command.scala:150)
2025.07.29 14:04:34 ERROR 	at sbt.Command$.$anonfun$applyEffect$2(Command.scala:145)
2025.07.29 14:04:34 ERROR 	at sbt.Command$.process(Command.scala:189)
2025.07.29 14:04:34 ERROR 	at sbt.MainLoop$.$anonfun$processCommand$5(MainLoop.scala:245)
2025.07.29 14:04:34 ERROR 	at scala.Option.getOrElse(Option.scala:189)
2025.07.29 14:04:34 ERROR 	at sbt.MainLoop$.process$1(MainLoop.scala:245)
2025.07.29 14:04:34 ERROR 	at sbt.MainLoop$.processCommand(MainLoop.scala:278)
2025.07.29 14:04:34 ERROR 	at sbt.MainLoop$.$anonfun$next$5(MainLoop.scala:163)
2025.07.29 14:04:34 ERROR 	at sbt.State$StateOpsImpl$.runCmd$1(State.scala:289)
2025.07.29 14:04:34 ERROR 	at sbt.State$StateOpsImpl$.process$extension(State.scala:325)
2025.07.29 14:04:34 ERROR 	at sbt.MainLoop$.$anonfun$next$4(MainLoop.scala:163)
2025.07.29 14:04:34 ERROR 	at sbt.internal.util.ErrorHandling$.wideConvert(ErrorHandling.scala:23)
2025.07.29 14:04:34 ERROR 	at sbt.MainLoop$.next(MainLoop.scala:163)
2025.07.29 14:04:34 ERROR 	at sbt.MainLoop$.run(MainLoop.scala:144)
2025.07.29 14:04:34 ERROR 	at sbt.MainLoop$.$anonfun$runWithNewLog$1(MainLoop.scala:119)
2025.07.29 14:04:34 ERROR 	at sbt.io.Using.apply(Using.scala:27)
2025.07.29 14:04:34 ERROR 	at sbt.MainLoop$.runWithNewLog(MainLoop.scala:112)
2025.07.29 14:04:34 ERROR 	at sbt.MainLoop$.runAndClearLast(MainLoop.scala:66)
2025.07.29 14:04:34 ERROR 	at sbt.MainLoop$.runLoggedLoop(MainLoop.scala:51)
2025.07.29 14:04:34 ERROR 	at sbt.MainLoop$.runLogged(MainLoop.scala:42)
2025.07.29 14:04:34 ERROR 	at sbt.StandardMain$.runManaged(Main.scala:215)
2025.07.29 14:04:34 ERROR 	at sbt.xMain$.$anonfun$run$11(Main.scala:133)
2025.07.29 14:04:34 ERROR 	at scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)
2025.07.29 14:04:34 ERROR 	at scala.Console$.withIn(Console.scala:230)
2025.07.29 14:04:34 ERROR 	at sbt.internal.util.Terminal$.withIn(Terminal.scala:569)
2025.07.29 14:04:34 ERROR 	at sbt.internal.util.Terminal$.$anonfun$withStreams$1(Terminal.scala:350)
2025.07.29 14:04:34 ERROR 	at scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)
2025.07.29 14:04:34 ERROR 	at scala.Console$.withOut(Console.scala:167)
2025.07.29 14:04:34 ERROR 	at sbt.internal.util.Terminal$.$anonfun$withOut$2(Terminal.scala:559)
2025.07.29 14:04:34 ERROR 	at scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)
2025.07.29 14:04:34 ERROR 	at scala.Console$.withErr(Console.scala:196)
2025.07.29 14:04:34 ERROR 	at sbt.internal.util.Terminal$.withOut(Terminal.scala:559)
2025.07.29 14:04:34 ERROR 	at sbt.internal.util.Terminal$.withStreams(Terminal.scala:350)
2025.07.29 14:04:34 ERROR 	at sbt.xMain$.withStreams$1(Main.scala:87)
2025.07.29 14:04:34 ERROR 	at sbt.xMain$.run(Main.scala:121)
2025.07.29 14:04:34 ERROR 	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
2025.07.29 14:04:34 ERROR 	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
2025.07.29 14:04:34 ERROR 	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
2025.07.29 14:04:34 ERROR 	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
2025.07.29 14:04:34 ERROR 	at sbt.internal.XMainConfiguration.run(XMainConfiguration.java:57)
2025.07.29 14:04:34 ERROR 	at sbt.xMain.run(Main.scala:46)
2025.07.29 14:04:34 ERROR 	at xsbt.boot.Launch$.$anonfun$run$1(Launch.scala:149)
2025.07.29 14:04:34 ERROR 	at xsbt.boot.Launch$.withContextLoader(Launch.scala:176)
2025.07.29 14:04:34 ERROR 	at xsbt.boot.Launch$.run(Launch.scala:149)
2025.07.29 14:04:34 ERROR 	at xsbt.boot.Launch$.$anonfun$apply$1(Launch.scala:44)
2025.07.29 14:04:34 ERROR 	at xsbt.boot.Launch$.launch(Launch.scala:159)
2025.07.29 14:04:34 ERROR 	at xsbt.boot.Launch$.apply(Launch.scala:44)
2025.07.29 14:04:34 ERROR 	at xsbt.boot.Launch$.apply(Launch.scala:21)
2025.07.29 14:04:34 ERROR 	at xsbt.boot.Boot$.runImpl(Boot.scala:78)
2025.07.29 14:04:34 ERROR 	at xsbt.boot.Boot$.run(Boot.scala:73)
2025.07.29 14:04:34 ERROR 	at xsbt.boot.Boot$.main(Boot.scala:21)
2025.07.29 14:04:34 ERROR 	at xsbt.boot.Boot.main(Boot.scala)
2025.07.29 14:04:34 INFO  [success] Generated .bloop/root.json
2025.07.29 14:04:34 INFO  [success] Generated .bloop/root-test.json
2025.07.29 14:04:34 INFO  [success] Total time: 1 s, completed Jul 29, 2025, 2:04:35 PM
2025.07.29 14:04:35 INFO  time: ran 'sbt bloopInstall' in 6.28s
2025.07.29 14:04:35 INFO  Disconnecting from Bloop session...
2025.07.29 14:04:35 INFO  Shut down connection with build server.
2025.07.29 14:04:35 INFO  Shut down connection with build server.
2025.07.29 14:04:35 INFO  Shut down connection with build server.
2025.07.29 14:04:35 INFO  Attempting to connect to the build server...
2025.07.29 14:04:35 INFO  Found a Bloop server running
2025.07.29 14:04:35 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /home/<USER>/projects/match-manager/.metals/bsp.trace.json or /home/<USER>/.cache/metals/bsp.trace.json
2025.07.29 14:04:36 INFO  Attempting to connect to the build server...
2025.07.29 14:04:36 INFO  Found a Bloop server running
2025.07.29 14:04:36 INFO  Attempting to connect to the build server...
2025.07.29 14:04:36 INFO  Found a Bloop server running
2025.07.29 14:04:37 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /home/<USER>/projects/match-manager/project/project/.metals/bsp.trace.json or /home/<USER>/.cache/metals/bsp.trace.json
2025.07.29 14:04:37 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /home/<USER>/projects/match-manager/project/.metals/bsp.trace.json or /home/<USER>/.cache/metals/bsp.trace.json
2025.07.29 14:04:39 INFO  time: Connected to build server in 3.58s
2025.07.29 14:04:39 INFO  Connected to Build server: Bloop v2.0.10
2025.07.29 14:04:40 INFO  time: indexed workspace in 1.82s
2025.07.29 14:04:40 INFO  compiling root (21 scala sources)
2025.07.29 14:04:45 INFO  time: compiled root in 4.79s
2025.07.29 14:04:45 INFO  compiling root-test (4 scala sources)
2025.07.29 14:04:45 INFO  time: compiled root-test in 0.56s
2025.07.29 14:05:20 INFO  scalafmt: excluded /home/<USER>/projects/match-manager/build.sbt (to format this file, update `project.excludeFilters` in .scalafmt.conf)
2025.07.29 14:05:20 INFO  skipping build import with status 'Installed'
2025.07.29 14:06:20 INFO  compiling root (1 scala source)
2025.07.29 14:06:20 INFO  time: compiled root in 0.83s
2025.07.29 14:08:06 INFO  compiling root (1 scala source)
2025.07.29 14:08:06 INFO  time: compiled root in 0.81s
2025.07.29 14:08:26 INFO  compiling root (1 scala source)
2025.07.29 14:08:26 INFO  time: compiled root in 0.73s
2025.07.29 14:09:54 INFO  compiling root (1 scala source)
2025.07.29 14:09:54 INFO  time: compiled root in 0.64s
2025.07.29 14:10:19 INFO  Started: Metals version 1.6.0 in folders '/home/<USER>/projects/match-manager' for client Visual Studio Code 1.102.2.
2025.07.29 14:10:20 INFO  Attempting to connect to the build server...
2025.07.29 14:10:20 INFO  Found a Bloop server running
Jul 29, 2025 2:10:20 PM io.undertow.Undertow start
INFO: starting server: Undertow - 2.3.12.Final
Jul 29, 2025 2:10:20 PM org.xnio.Xnio <clinit>
INFO: XNIO version 3.8.16.Final
Jul 29, 2025 2:10:20 PM org.xnio.nio.NioXnio <clinit>
INFO: XNIO NIO Implementation Version 3.8.16.Final
2025.07.29 14:10:20 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /home/<USER>/projects/match-manager/.metals/bsp.trace.json or /home/<USER>/.cache/metals/bsp.trace.json
Jul 29, 2025 2:10:20 PM org.jboss.threads.Version <clinit>
INFO: JBoss Threads version 3.5.0.Final
2025.07.29 14:10:20 ERROR Error starting MCP server
java.lang.RuntimeException: java.net.BindException: Address already in use
	at io.undertow.Undertow.start(Undertow.java:254)
	at scala.meta.internal.metals.mcp.MetalsMcpServer.run(MetalsMcpServer.scala:195)
	at scala.meta.internal.metals.ProjectMetalsLspService.$anonfun$startMcpServer$1(ProjectMetalsLspService.scala:261)
	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.scala:18)
	at scala.concurrent.Future$.$anonfun$apply$1(Future.scala:687)
	at scala.concurrent.impl.Promise$Transformation.run(Promise.scala:467)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.lang.Thread.run(Thread.java:1583)
Caused by: java.net.BindException: Address already in use
	at sun.nio.ch.Net.bind0(Native Method)
	at sun.nio.ch.Net.bind(Net.java:565)
	at sun.nio.ch.ServerSocketChannelImpl.netBind(ServerSocketChannelImpl.java:344)
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:301)
	at sun.nio.ch.ServerSocketAdaptor.bind(ServerSocketAdaptor.java:89)
	at org.xnio.nio.NioXnioWorker.createTcpConnectionServer(NioXnioWorker.java:178)
	at org.xnio.XnioWorker.createStreamConnectionServer(XnioWorker.java:310)
	at io.undertow.Undertow.start(Undertow.java:196)
	at scala.meta.internal.metals.mcp.MetalsMcpServer.run(MetalsMcpServer.scala:195)
	at scala.meta.internal.metals.ProjectMetalsLspService.$anonfun$startMcpServer$1(ProjectMetalsLspService.scala:261)
	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.scala:18)
	at scala.concurrent.Future$.$anonfun$apply$1(Future.scala:687)
	at scala.concurrent.impl.Promise$Transformation.run(Promise.scala:467)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.lang.Thread.run(Thread.java:1583)

2025.07.29 14:10:22 INFO  Attempting to connect to the build server...
2025.07.29 14:10:22 INFO  Found a Bloop server running
2025.07.29 14:10:22 INFO  Attempting to connect to the build server...
2025.07.29 14:10:22 INFO  Found a Bloop server running
2025.07.29 14:10:22 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /home/<USER>/projects/match-manager/project/project/.metals/bsp.trace.json or /home/<USER>/.cache/metals/bsp.trace.json
2025.07.29 14:10:22 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /home/<USER>/projects/match-manager/project/.metals/bsp.trace.json or /home/<USER>/.cache/metals/bsp.trace.json
2025.07.29 14:10:24 INFO  time: Connected to build server in 3.78s
2025.07.29 14:10:24 INFO  Connected to Build server: Bloop v2.0.10
2025.07.29 14:10:24 WARN  2.12.15 is no longer supported in the current Metals versions, using the last known supported version 1.3.3
