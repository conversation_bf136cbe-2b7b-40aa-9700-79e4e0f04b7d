{"version": "1.4.0", "project": {"name": "root", "directory": "/home/<USER>/projects/match-manager", "workspaceDir": "/home/<USER>/projects/match-manager", "sources": ["/home/<USER>/projects/match-manager/src/main/scala", "/home/<USER>/projects/match-manager/src/main/scala-2.13", "/home/<USER>/projects/match-manager/src/main/scala-2", "/home/<USER>/projects/match-manager/src/main/java", "/home/<USER>/projects/match-manager/target/scala-2.13/src_managed/main"], "dependencies": [], "classpath": ["/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.9/scala-library-2.13.9.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-ember-server_2.13/1.0.0-M29/http4s-ember-server_2.13-1.0.0-M29.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-ember-client_2.13/1.0.0-M29/http4s-ember-client_2.13-1.0.0-M29.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-circe_2.13/1.0.0-M29/http4s-circe_2.13-1.0.0-M29.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-dsl_2.13/1.0.0-M29/http4s-dsl_2.13-1.0.0-M29.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/ch/qos/logback/logback-classic/1.2.8/logback-classic-1.2.8.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/scalatags_2.13/0.10.0/scalatags_2.13-0.10.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/ekrich/sconfig_2.13/1.4.5/sconfig_2.13-1.4.5.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/tpolecat/doobie-core_2.13/1.0.0-RC1/doobie-core_2.13-1.0.0-RC1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/tpolecat/doobie-hikari_2.13/1.0.0-RC1/doobie-hikari_2.13-1.0.0-RC1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/net/snowflake/snowflake-jdbc/3.13.11/snowflake-jdbc-3.13.11.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-generic_2.13/0.15.0-M1/circe-generic_2.13-0.15.0-M1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/getquill/quill-jdbc_2.13/3.11.0/quill-jdbc_2.13-3.11.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/getquill/quill-core_2.13/3.11.0/quill-core_2.13-3.11.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/polyvariant/doobie-quill_2.13/0.0.2/doobie-quill_2.13-0.0.2.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/mysql/mysql-connector-java/8.0.25/mysql-connector-java-8.0.25.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-ember-core_2.13/1.0.0-M29/http4s-ember-core_2.13-1.0.0-M29.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-server_2.13/1.0.0-M29/http4s-server_2.13-1.0.0-M29.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/log4cats-slf4j_2.13/2.1.1/log4cats-slf4j_2.13-2.1.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-client_2.13/1.0.0-M29/http4s-client_2.13-1.0.0-M29.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/keypool_2.13/0.4.7/keypool_2.13-0.4.7.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-core_2.13/1.0.0-M29/http4s-core_2.13-1.0.0-M29.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-jawn_2.13/1.0.0-M29/http4s-jawn_2.13-1.0.0-M29.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-core_2.13/0.15.0-M1/circe-core_2.13-0.15.0-M1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-jawn_2.13/0.15.0-M1/circe-jawn_2.13-0.15.0-M1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/ch/qos/logback/logback-core/1.2.8/logback-core-1.2.8.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.0-alpha1/slf4j-api-2.0.0-alpha1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/sourcecode_2.13/0.2.3/sourcecode_2.13-0.2.3.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/geny_2.13/0.6.7/geny_2.13-0.6.7.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/tpolecat/doobie-free_2.13/1.0.0-RC1/doobie-free_2.13-1.0.0-RC1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.5.0/scala-collection-compat_2.13-2.5.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/chuusai/shapeless_2.13/2.3.7/shapeless_2.13-2.3.7.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/tpolecat/typename_2.13/1.0.0/typename_2.13-1.0.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/zaxxer/HikariCP/4.0.3/HikariCP-4.0.3.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/getquill/quill-sql_2.13/3.11.0/quill-sql_2.13-3.11.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/pprint_2.13/0.5.9/pprint_2.13-0.5.9.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/getquill/quill-core-portable_2.13/3.11.0/quill-core-portable_2.13-3.11.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/typesafe/config/1.4.1/config-1.4.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/dev/zio/zio-logging_2.13/0.5.13/zio-logging_2.13-0.5.13.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.4/scala-logging_2.13-3.9.4.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.9/scala-reflect-2.13.9.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/google/protobuf/protobuf-java/3.11.4/protobuf-java-3.11.4.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/log4cats-core_2.13/2.1.1/log4cats-core_2.13-2.1.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect_2.13/3.2.9/cats-effect_2.13-3.2.9.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-core_2.13/2.6.1/cats-core_2.13-2.6.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect-kernel_2.13/3.2.9/cats-effect-kernel_2.13-3.2.9.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/case-insensitive_2.13/1.1.4/case-insensitive_2.13-1.1.4.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect-std_2.13/3.2.9/cats-effect-std_2.13-3.2.9.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-parse_2.13/0.3.4/cats-parse_2.13-0.3.4.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-crypto_2.13/0.2.0/http4s-crypto_2.13-0.2.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/co/fs2/fs2-core_2.13/3.1.5/fs2-core_2.13-3.1.5.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/co/fs2/fs2-io_2.13/3.1.5/fs2-io_2.13-3.1.5.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/comcast/ip4s-core_2.13/3.0.4/ip4s-core_2.13-3.0.4.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/literally_2.13/1.0.2/literally_2.13-1.0.2.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/log4s/log4s_2.13/1.10.0/log4s_2.13-1.10.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scodec/scodec-bits_2.13/1.1.29/scodec-bits_2.13-1.1.29.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/vault_2.13/3.1.0/vault_2.13-3.1.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/jawn-fs2_2.13/2.1.0/jawn-fs2_2.13-2.1.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/jawn-parser_2.13/1.2.0/jawn-parser_2.13-1.2.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-numbers_2.13/0.15.0-M1/circe-numbers_2.13-0.15.0-M1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-free_2.13/2.6.1/cats-free_2.13-2.6.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/getquill/quill-sql-portable_2.13/3.11.0/quill-sql-portable_2.13-3.11.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/github/vertical-blank/scala-sql-formatter_2.13/1.0.1/scala-sql-formatter_2.13-1.0.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/fansi_2.13/0.2.9/fansi_2.13-0.2.9.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/twitter/chill_2.13/0.10.0/chill_2.13-0.10.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/suzaku/boopickle_2.13/1.3.1/boopickle_2.13-1.3.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/dev/zio/zio_2.13/1.0.12/zio_2.13-1.0.12.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/dev/zio/zio-streams_2.13/1.0.12/zio-streams_2.13-1.0.12.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-kernel_2.13/2.6.1/cats-kernel_2.13-2.6.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/simulacrum-scalafix-annotations_2.13/0.5.4/simulacrum-scalafix-annotations_2.13-0.5.4.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/github/vertical-blank/sql-formatter/1.0/sql-formatter-1.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/twitter/chill-java/0.10.0/chill-java-0.10.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/esotericsoftware/kryo-shaded/4.0.2/kryo-shaded-4.0.2.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/apache/xbean/xbean-asm7-shaded/4.15/xbean-asm7-shaded-4.15.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/dev/zio/zio-stacktracer_2.13/1.0.12/zio-stacktracer_2.13-1.0.12.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/dev/zio/izumi-reflect_2.13/1.1.3/izumi-reflect_2.13-1.1.3.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/esotericsoftware/minlog/1.3.0/minlog-1.3.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/objenesis/objenesis/2.5.1/objenesis-2.5.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/dev/zio/izumi-reflect-thirdparty-boopickle-shaded_2.13/1.1.3/izumi-reflect-thirdparty-boopickle-shaded_2.13-1.1.3.jar"], "out": "/home/<USER>/projects/match-manager/.bloop/root", "classesDir": "/home/<USER>/projects/match-manager/.bloop/root/scala-2.13/classes", "resources": ["/home/<USER>/projects/match-manager/src/main/resources", "/home/<USER>/projects/match-manager/target/scala-2.13/resource_managed/main"], "scala": {"organization": "org.scala-lang", "name": "scala-compiler", "version": "2.13.9", "options": ["-encoding", "utf8", "-explaintypes", "-feature", "-language:existentials", "-language:experimental.macros", "-language:higherKinds", "-language:implicitConversions", "-unchecked", "-<PERSON><PERSON><PERSON><PERSON><PERSON>", "-Xfatal-warnings", "-<PERSON><PERSON>:adapted-args", "-Xlint:constant", "-Xlint:delayedinit-select", "-Xlint:deprecation", "-<PERSON><PERSON>:doc-detached", "-Xlint:inaccessible", "-Xlint:infer-any", "-<PERSON><PERSON>:missing-interpolator", "-Xlint:nullary-unit", "-Xlint:option-implicit", "-Xlint:package-object-classes", "-Xlint:poly-implicit-overload", "-<PERSON><PERSON>:private-shadow", "-Xlint:stars-align", "-<PERSON><PERSON>:strict-unsealed-patmat", "-Xlint:type-parameter-shadow", "-Xlint:-byname-implicit", "-Wunused:nowarn", "-Wdead-code", "-Wextra-implicit", "-Wnumeric-widen", "-Wunused:implicits", "-Wunused:explicits", "-Wunused:imports", "-Wunused:locals", "-Wunused:params", "-Wunused:pat<PERSON><PERSON>", "-Wunused:privates", "-Wvalue-discard", "-Vimplicits", "-Vtype-diffs"], "jars": ["/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.9/scala-library-2.13.9.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.13.9/scala-compiler-2.13.9.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.9/scala-reflect-2.13.9.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/github/java-diff-utils/java-diff-utils/4.12/java-diff-utils-4.12.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/jline/jline/3.21.0/jline-3.21.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/net/java/dev/jna/jna/5.9.0/jna-5.9.0.jar"], "setup": {"order": "mixed", "addLibraryToBootClasspath": true, "addCompilerToClasspath": false, "addExtraJarsToClasspath": false, "manageBootClasspath": true, "filterLibraryFromClasspath": true}}, "java": {"options": []}, "test": {"frameworks": [{"names": ["org.scalacheck.ScalaCheckFramework"]}, {"names": ["org.specs2.runner.Specs2Framework", "org.specs2.runner.SpecsFramework"]}, {"names": ["org.specs.runner.SpecsFramework"]}, {"names": ["org.scalatest.tools.Framework", "org.scalatest.tools.ScalaTestFramework"]}, {"names": ["com.novocode.junit.JUnitFramework"]}, {"names": ["munit.Framework"]}, {"names": ["munit.Framework"]}], "options": {"excludes": [], "arguments": []}}, "platform": {"name": "jvm", "config": {"home": "/home/<USER>/.cache/coursier/arc/https/github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.15%252B6/OpenJDK17U-jdk_x64_linux_hotspot_17.0.15_6.tar.gz/jdk-17.0.15+6", "options": ["-Duser.dir=/home/<USER>/projects/match-manager"]}, "mainClass": [], "runtimeConfig": {"home": "/home/<USER>/.cache/coursier/arc/https/github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.15%252B6/OpenJDK17U-jdk_x64_linux_hotspot_17.0.15_6.tar.gz/jdk-17.0.15+6", "options": ["-Duser.dir=/home/<USER>/projects/match-manager"]}, "classpath": ["/home/<USER>/projects/match-manager/.bloop/root/scala-2.13/classes", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.9/scala-library-2.13.9.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-ember-server_2.13/1.0.0-M29/http4s-ember-server_2.13-1.0.0-M29.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-ember-client_2.13/1.0.0-M29/http4s-ember-client_2.13-1.0.0-M29.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-circe_2.13/1.0.0-M29/http4s-circe_2.13-1.0.0-M29.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-dsl_2.13/1.0.0-M29/http4s-dsl_2.13-1.0.0-M29.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/ch/qos/logback/logback-classic/1.2.8/logback-classic-1.2.8.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/scalatags_2.13/0.10.0/scalatags_2.13-0.10.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/ekrich/sconfig_2.13/1.4.5/sconfig_2.13-1.4.5.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/tpolecat/doobie-core_2.13/1.0.0-RC1/doobie-core_2.13-1.0.0-RC1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/tpolecat/doobie-hikari_2.13/1.0.0-RC1/doobie-hikari_2.13-1.0.0-RC1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/net/snowflake/snowflake-jdbc/3.13.11/snowflake-jdbc-3.13.11.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-generic_2.13/0.15.0-M1/circe-generic_2.13-0.15.0-M1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/getquill/quill-jdbc_2.13/3.11.0/quill-jdbc_2.13-3.11.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/getquill/quill-core_2.13/3.11.0/quill-core_2.13-3.11.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/polyvariant/doobie-quill_2.13/0.0.2/doobie-quill_2.13-0.0.2.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/mysql/mysql-connector-java/8.0.25/mysql-connector-java-8.0.25.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-ember-core_2.13/1.0.0-M29/http4s-ember-core_2.13-1.0.0-M29.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-server_2.13/1.0.0-M29/http4s-server_2.13-1.0.0-M29.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/log4cats-slf4j_2.13/2.1.1/log4cats-slf4j_2.13-2.1.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-client_2.13/1.0.0-M29/http4s-client_2.13-1.0.0-M29.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/keypool_2.13/0.4.7/keypool_2.13-0.4.7.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-core_2.13/1.0.0-M29/http4s-core_2.13-1.0.0-M29.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-jawn_2.13/1.0.0-M29/http4s-jawn_2.13-1.0.0-M29.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-core_2.13/0.15.0-M1/circe-core_2.13-0.15.0-M1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-jawn_2.13/0.15.0-M1/circe-jawn_2.13-0.15.0-M1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/ch/qos/logback/logback-core/1.2.8/logback-core-1.2.8.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.0-alpha1/slf4j-api-2.0.0-alpha1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/sourcecode_2.13/0.2.3/sourcecode_2.13-0.2.3.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/geny_2.13/0.6.7/geny_2.13-0.6.7.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/tpolecat/doobie-free_2.13/1.0.0-RC1/doobie-free_2.13-1.0.0-RC1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.5.0/scala-collection-compat_2.13-2.5.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/chuusai/shapeless_2.13/2.3.7/shapeless_2.13-2.3.7.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/tpolecat/typename_2.13/1.0.0/typename_2.13-1.0.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/zaxxer/HikariCP/4.0.3/HikariCP-4.0.3.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/getquill/quill-sql_2.13/3.11.0/quill-sql_2.13-3.11.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/pprint_2.13/0.5.9/pprint_2.13-0.5.9.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/getquill/quill-core-portable_2.13/3.11.0/quill-core-portable_2.13-3.11.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/typesafe/config/1.4.1/config-1.4.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/dev/zio/zio-logging_2.13/0.5.13/zio-logging_2.13-0.5.13.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.4/scala-logging_2.13-3.9.4.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.9/scala-reflect-2.13.9.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/google/protobuf/protobuf-java/3.11.4/protobuf-java-3.11.4.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/log4cats-core_2.13/2.1.1/log4cats-core_2.13-2.1.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect_2.13/3.2.9/cats-effect_2.13-3.2.9.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-core_2.13/2.6.1/cats-core_2.13-2.6.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect-kernel_2.13/3.2.9/cats-effect-kernel_2.13-3.2.9.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/case-insensitive_2.13/1.1.4/case-insensitive_2.13-1.1.4.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect-std_2.13/3.2.9/cats-effect-std_2.13-3.2.9.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-parse_2.13/0.3.4/cats-parse_2.13-0.3.4.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-crypto_2.13/0.2.0/http4s-crypto_2.13-0.2.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/co/fs2/fs2-core_2.13/3.1.5/fs2-core_2.13-3.1.5.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/co/fs2/fs2-io_2.13/3.1.5/fs2-io_2.13-3.1.5.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/comcast/ip4s-core_2.13/3.0.4/ip4s-core_2.13-3.0.4.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/literally_2.13/1.0.2/literally_2.13-1.0.2.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/log4s/log4s_2.13/1.10.0/log4s_2.13-1.10.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scodec/scodec-bits_2.13/1.1.29/scodec-bits_2.13-1.1.29.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/vault_2.13/3.1.0/vault_2.13-3.1.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/jawn-fs2_2.13/2.1.0/jawn-fs2_2.13-2.1.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/jawn-parser_2.13/1.2.0/jawn-parser_2.13-1.2.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-numbers_2.13/0.15.0-M1/circe-numbers_2.13-0.15.0-M1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-free_2.13/2.6.1/cats-free_2.13-2.6.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/getquill/quill-sql-portable_2.13/3.11.0/quill-sql-portable_2.13-3.11.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/github/vertical-blank/scala-sql-formatter_2.13/1.0.1/scala-sql-formatter_2.13-1.0.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/fansi_2.13/0.2.9/fansi_2.13-0.2.9.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/twitter/chill_2.13/0.10.0/chill_2.13-0.10.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/suzaku/boopickle_2.13/1.3.1/boopickle_2.13-1.3.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/dev/zio/zio_2.13/1.0.12/zio_2.13-1.0.12.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/dev/zio/zio-streams_2.13/1.0.12/zio-streams_2.13-1.0.12.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-kernel_2.13/2.6.1/cats-kernel_2.13-2.6.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/simulacrum-scalafix-annotations_2.13/0.5.4/simulacrum-scalafix-annotations_2.13-0.5.4.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/github/vertical-blank/sql-formatter/1.0/sql-formatter-1.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/twitter/chill-java/0.10.0/chill-java-0.10.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/esotericsoftware/kryo-shaded/4.0.2/kryo-shaded-4.0.2.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/apache/xbean/xbean-asm7-shaded/4.15/xbean-asm7-shaded-4.15.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/dev/zio/zio-stacktracer_2.13/1.0.12/zio-stacktracer_2.13-1.0.12.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/dev/zio/izumi-reflect_2.13/1.1.3/izumi-reflect_2.13-1.1.3.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/esotericsoftware/minlog/1.3.0/minlog-1.3.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/objenesis/objenesis/2.5.1/objenesis-2.5.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/dev/zio/izumi-reflect-thirdparty-boopickle-shaded_2.13/1.1.3/izumi-reflect-thirdparty-boopickle-shaded_2.13-1.1.3.jar"]}, "resolution": {"modules": [{"organization": "org.scala-lang", "name": "scala-library", "version": "2.13.9", "configurations": "default", "artifacts": [{"name": "scala-library", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.9/scala-library-2.13.9.jar"}, {"name": "scala-library", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.9/scala-library-2.13.9-sources.jar"}]}, {"organization": "org.http4s", "name": "http4s-ember-server_2.13", "version": "1.0.0-M29", "configurations": "default", "artifacts": [{"name": "http4s-ember-server_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-ember-server_2.13/1.0.0-M29/http4s-ember-server_2.13-1.0.0-M29.jar"}, {"name": "http4s-ember-server_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-ember-server_2.13/1.0.0-M29/http4s-ember-server_2.13-1.0.0-M29-sources.jar"}]}, {"organization": "org.http4s", "name": "http4s-ember-client_2.13", "version": "1.0.0-M29", "configurations": "default", "artifacts": [{"name": "http4s-ember-client_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-ember-client_2.13/1.0.0-M29/http4s-ember-client_2.13-1.0.0-M29.jar"}, {"name": "http4s-ember-client_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-ember-client_2.13/1.0.0-M29/http4s-ember-client_2.13-1.0.0-M29-sources.jar"}]}, {"organization": "org.http4s", "name": "http4s-circe_2.13", "version": "1.0.0-M29", "configurations": "default", "artifacts": [{"name": "http4s-circe_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-circe_2.13/1.0.0-M29/http4s-circe_2.13-1.0.0-M29.jar"}, {"name": "http4s-circe_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-circe_2.13/1.0.0-M29/http4s-circe_2.13-1.0.0-M29-sources.jar"}]}, {"organization": "org.http4s", "name": "http4s-dsl_2.13", "version": "1.0.0-M29", "configurations": "default", "artifacts": [{"name": "http4s-dsl_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-dsl_2.13/1.0.0-M29/http4s-dsl_2.13-1.0.0-M29.jar"}, {"name": "http4s-dsl_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-dsl_2.13/1.0.0-M29/http4s-dsl_2.13-1.0.0-M29-sources.jar"}]}, {"organization": "ch.qos.logback", "name": "logback-classic", "version": "1.2.8", "configurations": "default", "artifacts": [{"name": "logback-classic", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/ch/qos/logback/logback-classic/1.2.8/logback-classic-1.2.8.jar"}, {"name": "logback-classic", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/ch/qos/logback/logback-classic/1.2.8/logback-classic-1.2.8-sources.jar"}]}, {"organization": "com.lihaoyi", "name": "scalatags_2.13", "version": "0.10.0", "configurations": "default", "artifacts": [{"name": "scalatags_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/scalatags_2.13/0.10.0/scalatags_2.13-0.10.0.jar"}, {"name": "scalatags_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/scalatags_2.13/0.10.0/scalatags_2.13-0.10.0-sources.jar"}]}, {"organization": "org.ekrich", "name": "sconfig_2.13", "version": "1.4.5", "configurations": "default", "artifacts": [{"name": "sconfig_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/ekrich/sconfig_2.13/1.4.5/sconfig_2.13-1.4.5.jar"}, {"name": "sconfig_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/ekrich/sconfig_2.13/1.4.5/sconfig_2.13-1.4.5-sources.jar"}]}, {"organization": "org.tpolecat", "name": "doobie-core_2.13", "version": "1.0.0-RC1", "configurations": "default", "artifacts": [{"name": "doobie-core_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/tpolecat/doobie-core_2.13/1.0.0-RC1/doobie-core_2.13-1.0.0-RC1.jar"}, {"name": "doobie-core_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/tpolecat/doobie-core_2.13/1.0.0-RC1/doobie-core_2.13-1.0.0-RC1-sources.jar"}]}, {"organization": "org.tpolecat", "name": "doobie-hikari_2.13", "version": "1.0.0-RC1", "configurations": "default", "artifacts": [{"name": "doobie-hikari_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/tpolecat/doobie-hikari_2.13/1.0.0-RC1/doobie-hikari_2.13-1.0.0-RC1.jar"}, {"name": "doobie-hikari_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/tpolecat/doobie-hikari_2.13/1.0.0-RC1/doobie-hikari_2.13-1.0.0-RC1-sources.jar"}]}, {"organization": "net.snowflake", "name": "snowflake-jdbc", "version": "3.13.11", "configurations": "default", "artifacts": [{"name": "snowflake-jdbc", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/net/snowflake/snowflake-jdbc/3.13.11/snowflake-jdbc-3.13.11.jar"}, {"name": "snowflake-jdbc", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/net/snowflake/snowflake-jdbc/3.13.11/snowflake-jdbc-3.13.11-sources.jar"}]}, {"organization": "io.circe", "name": "circe-generic_2.13", "version": "0.15.0-M1", "configurations": "default", "artifacts": [{"name": "circe-generic_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-generic_2.13/0.15.0-M1/circe-generic_2.13-0.15.0-M1.jar"}, {"name": "circe-generic_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-generic_2.13/0.15.0-M1/circe-generic_2.13-0.15.0-M1-sources.jar"}]}, {"organization": "io.getquill", "name": "quill-jdbc_2.13", "version": "3.11.0", "configurations": "default", "artifacts": [{"name": "quill-jdbc_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/getquill/quill-jdbc_2.13/3.11.0/quill-jdbc_2.13-3.11.0.jar"}, {"name": "quill-jdbc_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/getquill/quill-jdbc_2.13/3.11.0/quill-jdbc_2.13-3.11.0-sources.jar"}]}, {"organization": "io.getquill", "name": "quill-core_2.13", "version": "3.11.0", "configurations": "default", "artifacts": [{"name": "quill-core_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/getquill/quill-core_2.13/3.11.0/quill-core_2.13-3.11.0.jar"}, {"name": "quill-core_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/getquill/quill-core_2.13/3.11.0/quill-core_2.13-3.11.0-sources.jar"}]}, {"organization": "org.polyvariant", "name": "doobie-quill_2.13", "version": "0.0.2", "configurations": "default", "artifacts": [{"name": "doobie-quill_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/polyvariant/doobie-quill_2.13/0.0.2/doobie-quill_2.13-0.0.2.jar"}, {"name": "doobie-quill_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/polyvariant/doobie-quill_2.13/0.0.2/doobie-quill_2.13-0.0.2-sources.jar"}]}, {"organization": "mysql", "name": "mysql-connector-java", "version": "8.0.25", "configurations": "default", "artifacts": [{"name": "mysql-connector-java", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/mysql/mysql-connector-java/8.0.25/mysql-connector-java-8.0.25.jar"}, {"name": "mysql-connector-java", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/mysql/mysql-connector-java/8.0.25/mysql-connector-java-8.0.25-sources.jar"}]}, {"organization": "org.http4s", "name": "http4s-ember-core_2.13", "version": "1.0.0-M29", "configurations": "default", "artifacts": [{"name": "http4s-ember-core_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-ember-core_2.13/1.0.0-M29/http4s-ember-core_2.13-1.0.0-M29.jar"}, {"name": "http4s-ember-core_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-ember-core_2.13/1.0.0-M29/http4s-ember-core_2.13-1.0.0-M29-sources.jar"}]}, {"organization": "org.http4s", "name": "http4s-server_2.13", "version": "1.0.0-M29", "configurations": "default", "artifacts": [{"name": "http4s-server_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-server_2.13/1.0.0-M29/http4s-server_2.13-1.0.0-M29.jar"}, {"name": "http4s-server_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-server_2.13/1.0.0-M29/http4s-server_2.13-1.0.0-M29-sources.jar"}]}, {"organization": "org.typelevel", "name": "log4cats-slf4j_2.13", "version": "2.1.1", "configurations": "default", "artifacts": [{"name": "log4cats-slf4j_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/log4cats-slf4j_2.13/2.1.1/log4cats-slf4j_2.13-2.1.1.jar"}, {"name": "log4cats-slf4j_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/log4cats-slf4j_2.13/2.1.1/log4cats-slf4j_2.13-2.1.1-sources.jar"}]}, {"organization": "org.http4s", "name": "http4s-client_2.13", "version": "1.0.0-M29", "configurations": "default", "artifacts": [{"name": "http4s-client_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-client_2.13/1.0.0-M29/http4s-client_2.13-1.0.0-M29.jar"}, {"name": "http4s-client_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-client_2.13/1.0.0-M29/http4s-client_2.13-1.0.0-M29-sources.jar"}]}, {"organization": "org.typelevel", "name": "keypool_2.13", "version": "0.4.7", "configurations": "default", "artifacts": [{"name": "keypool_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/keypool_2.13/0.4.7/keypool_2.13-0.4.7.jar"}, {"name": "keypool_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/keypool_2.13/0.4.7/keypool_2.13-0.4.7-sources.jar"}]}, {"organization": "org.http4s", "name": "http4s-core_2.13", "version": "1.0.0-M29", "configurations": "default", "artifacts": [{"name": "http4s-core_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-core_2.13/1.0.0-M29/http4s-core_2.13-1.0.0-M29.jar"}, {"name": "http4s-core_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-core_2.13/1.0.0-M29/http4s-core_2.13-1.0.0-M29-sources.jar"}]}, {"organization": "org.http4s", "name": "http4s-jawn_2.13", "version": "1.0.0-M29", "configurations": "default", "artifacts": [{"name": "http4s-jawn_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-jawn_2.13/1.0.0-M29/http4s-jawn_2.13-1.0.0-M29.jar"}, {"name": "http4s-jawn_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-jawn_2.13/1.0.0-M29/http4s-jawn_2.13-1.0.0-M29-sources.jar"}]}, {"organization": "io.circe", "name": "circe-core_2.13", "version": "0.15.0-M1", "configurations": "default", "artifacts": [{"name": "circe-core_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-core_2.13/0.15.0-M1/circe-core_2.13-0.15.0-M1.jar"}, {"name": "circe-core_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-core_2.13/0.15.0-M1/circe-core_2.13-0.15.0-M1-sources.jar"}]}, {"organization": "io.circe", "name": "circe-jawn_2.13", "version": "0.15.0-M1", "configurations": "default", "artifacts": [{"name": "circe-jawn_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-jawn_2.13/0.15.0-M1/circe-jawn_2.13-0.15.0-M1.jar"}, {"name": "circe-jawn_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-jawn_2.13/0.15.0-M1/circe-jawn_2.13-0.15.0-M1-sources.jar"}]}, {"organization": "ch.qos.logback", "name": "logback-core", "version": "1.2.8", "configurations": "default", "artifacts": [{"name": "logback-core", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/ch/qos/logback/logback-core/1.2.8/logback-core-1.2.8.jar"}, {"name": "logback-core", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/ch/qos/logback/logback-core/1.2.8/logback-core-1.2.8-sources.jar"}]}, {"organization": "org.slf4j", "name": "slf4j-api", "version": "2.0.0-alpha1", "configurations": "default", "artifacts": [{"name": "slf4j-api", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.0-alpha1/slf4j-api-2.0.0-alpha1.jar"}, {"name": "slf4j-api", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.0-alpha1/slf4j-api-2.0.0-alpha1-sources.jar"}]}, {"organization": "com.lihaoyi", "name": "sourcecode_2.13", "version": "0.2.3", "configurations": "default", "artifacts": [{"name": "sourcecode_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/sourcecode_2.13/0.2.3/sourcecode_2.13-0.2.3.jar"}, {"name": "sourcecode_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/sourcecode_2.13/0.2.3/sourcecode_2.13-0.2.3-sources.jar"}]}, {"organization": "com.lihaoyi", "name": "geny_2.13", "version": "0.6.7", "configurations": "default", "artifacts": [{"name": "geny_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/geny_2.13/0.6.7/geny_2.13-0.6.7.jar"}, {"name": "geny_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/geny_2.13/0.6.7/geny_2.13-0.6.7-sources.jar"}]}, {"organization": "org.tpolecat", "name": "doobie-free_2.13", "version": "1.0.0-RC1", "configurations": "default", "artifacts": [{"name": "doobie-free_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/tpolecat/doobie-free_2.13/1.0.0-RC1/doobie-free_2.13-1.0.0-RC1.jar"}, {"name": "doobie-free_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/tpolecat/doobie-free_2.13/1.0.0-RC1/doobie-free_2.13-1.0.0-RC1-sources.jar"}]}, {"organization": "org.scala-lang.modules", "name": "scala-collection-compat_2.13", "version": "2.5.0", "configurations": "default", "artifacts": [{"name": "scala-collection-compat_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.5.0/scala-collection-compat_2.13-2.5.0.jar"}, {"name": "scala-collection-compat_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.5.0/scala-collection-compat_2.13-2.5.0-sources.jar"}]}, {"organization": "com.chuusai", "name": "shapeless_2.13", "version": "2.3.7", "configurations": "default", "artifacts": [{"name": "shapeless_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/chuusai/shapeless_2.13/2.3.7/shapeless_2.13-2.3.7.jar"}, {"name": "shapeless_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/chuusai/shapeless_2.13/2.3.7/shapeless_2.13-2.3.7-sources.jar"}]}, {"organization": "org.tpolecat", "name": "typename_2.13", "version": "1.0.0", "configurations": "default", "artifacts": [{"name": "typename_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/tpolecat/typename_2.13/1.0.0/typename_2.13-1.0.0.jar"}, {"name": "typename_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/tpolecat/typename_2.13/1.0.0/typename_2.13-1.0.0-sources.jar"}]}, {"organization": "com.zaxxer", "name": "HikariCP", "version": "4.0.3", "configurations": "default", "artifacts": [{"name": "HikariCP", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/zaxxer/HikariCP/4.0.3/HikariCP-4.0.3.jar"}, {"name": "HikariCP", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/zaxxer/HikariCP/4.0.3/HikariCP-4.0.3-sources.jar"}]}, {"organization": "io.getquill", "name": "quill-sql_2.13", "version": "3.11.0", "configurations": "default", "artifacts": [{"name": "quill-sql_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/getquill/quill-sql_2.13/3.11.0/quill-sql_2.13-3.11.0.jar"}, {"name": "quill-sql_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/getquill/quill-sql_2.13/3.11.0/quill-sql_2.13-3.11.0-sources.jar"}]}, {"organization": "com.lihaoyi", "name": "pprint_2.13", "version": "0.5.9", "configurations": "default", "artifacts": [{"name": "pprint_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/pprint_2.13/0.5.9/pprint_2.13-0.5.9.jar"}, {"name": "pprint_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/pprint_2.13/0.5.9/pprint_2.13-0.5.9-sources.jar"}]}, {"organization": "io.getquill", "name": "quill-core-portable_2.13", "version": "3.11.0", "configurations": "default", "artifacts": [{"name": "quill-core-portable_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/getquill/quill-core-portable_2.13/3.11.0/quill-core-portable_2.13-3.11.0.jar"}, {"name": "quill-core-portable_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/getquill/quill-core-portable_2.13/3.11.0/quill-core-portable_2.13-3.11.0-sources.jar"}]}, {"organization": "com.typesafe", "name": "config", "version": "1.4.1", "configurations": "default", "artifacts": [{"name": "config", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/typesafe/config/1.4.1/config-1.4.1.jar"}, {"name": "config", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/typesafe/config/1.4.1/config-1.4.1-sources.jar"}]}, {"organization": "dev.zio", "name": "zio-logging_2.13", "version": "0.5.13", "configurations": "default", "artifacts": [{"name": "zio-logging_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/dev/zio/zio-logging_2.13/0.5.13/zio-logging_2.13-0.5.13.jar"}, {"name": "zio-logging_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/dev/zio/zio-logging_2.13/0.5.13/zio-logging_2.13-0.5.13-sources.jar"}]}, {"organization": "com.typesafe.scala-logging", "name": "scala-logging_2.13", "version": "3.9.4", "configurations": "default", "artifacts": [{"name": "scala-logging_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.4/scala-logging_2.13-3.9.4.jar"}, {"name": "scala-logging_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.4/scala-logging_2.13-3.9.4-sources.jar"}]}, {"organization": "org.scala-lang", "name": "scala-reflect", "version": "2.13.9", "configurations": "default", "artifacts": [{"name": "scala-reflect", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.9/scala-reflect-2.13.9.jar"}, {"name": "scala-reflect", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.9/scala-reflect-2.13.9-sources.jar"}]}, {"organization": "com.google.protobuf", "name": "protobuf-java", "version": "3.11.4", "configurations": "default", "artifacts": [{"name": "protobuf-java", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/google/protobuf/protobuf-java/3.11.4/protobuf-java-3.11.4.jar"}, {"name": "protobuf-java", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/google/protobuf/protobuf-java/3.11.4/protobuf-java-3.11.4-sources.jar"}]}, {"organization": "org.typelevel", "name": "log4cats-core_2.13", "version": "2.1.1", "configurations": "default", "artifacts": [{"name": "log4cats-core_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/log4cats-core_2.13/2.1.1/log4cats-core_2.13-2.1.1.jar"}, {"name": "log4cats-core_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/log4cats-core_2.13/2.1.1/log4cats-core_2.13-2.1.1-sources.jar"}]}, {"organization": "org.typelevel", "name": "cats-effect_2.13", "version": "3.2.9", "configurations": "default", "artifacts": [{"name": "cats-effect_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect_2.13/3.2.9/cats-effect_2.13-3.2.9.jar"}, {"name": "cats-effect_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect_2.13/3.2.9/cats-effect_2.13-3.2.9-sources.jar"}]}, {"organization": "org.typelevel", "name": "cats-core_2.13", "version": "2.6.1", "configurations": "default", "artifacts": [{"name": "cats-core_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-core_2.13/2.6.1/cats-core_2.13-2.6.1.jar"}, {"name": "cats-core_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-core_2.13/2.6.1/cats-core_2.13-2.6.1-sources.jar"}]}, {"organization": "org.typelevel", "name": "cats-effect-kernel_2.13", "version": "3.2.9", "configurations": "default", "artifacts": [{"name": "cats-effect-kernel_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect-kernel_2.13/3.2.9/cats-effect-kernel_2.13-3.2.9.jar"}, {"name": "cats-effect-kernel_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect-kernel_2.13/3.2.9/cats-effect-kernel_2.13-3.2.9-sources.jar"}]}, {"organization": "org.typelevel", "name": "case-insensitive_2.13", "version": "1.1.4", "configurations": "default", "artifacts": [{"name": "case-insensitive_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/case-insensitive_2.13/1.1.4/case-insensitive_2.13-1.1.4.jar"}, {"name": "case-insensitive_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/case-insensitive_2.13/1.1.4/case-insensitive_2.13-1.1.4-sources.jar"}]}, {"organization": "org.typelevel", "name": "cats-effect-std_2.13", "version": "3.2.9", "configurations": "default", "artifacts": [{"name": "cats-effect-std_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect-std_2.13/3.2.9/cats-effect-std_2.13-3.2.9.jar"}, {"name": "cats-effect-std_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect-std_2.13/3.2.9/cats-effect-std_2.13-3.2.9-sources.jar"}]}, {"organization": "org.typelevel", "name": "cats-parse_2.13", "version": "0.3.4", "configurations": "default", "artifacts": [{"name": "cats-parse_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-parse_2.13/0.3.4/cats-parse_2.13-0.3.4.jar"}, {"name": "cats-parse_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-parse_2.13/0.3.4/cats-parse_2.13-0.3.4-sources.jar"}]}, {"organization": "org.http4s", "name": "http4s-crypto_2.13", "version": "0.2.0", "configurations": "default", "artifacts": [{"name": "http4s-crypto_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-crypto_2.13/0.2.0/http4s-crypto_2.13-0.2.0.jar"}, {"name": "http4s-crypto_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-crypto_2.13/0.2.0/http4s-crypto_2.13-0.2.0-sources.jar"}]}, {"organization": "co.fs2", "name": "fs2-core_2.13", "version": "3.1.5", "configurations": "default", "artifacts": [{"name": "fs2-core_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/co/fs2/fs2-core_2.13/3.1.5/fs2-core_2.13-3.1.5.jar"}, {"name": "fs2-core_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/co/fs2/fs2-core_2.13/3.1.5/fs2-core_2.13-3.1.5-sources.jar"}]}, {"organization": "co.fs2", "name": "fs2-io_2.13", "version": "3.1.5", "configurations": "default", "artifacts": [{"name": "fs2-io_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/co/fs2/fs2-io_2.13/3.1.5/fs2-io_2.13-3.1.5.jar"}, {"name": "fs2-io_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/co/fs2/fs2-io_2.13/3.1.5/fs2-io_2.13-3.1.5-sources.jar"}]}, {"organization": "com.comcast", "name": "ip4s-core_2.13", "version": "3.0.4", "configurations": "default", "artifacts": [{"name": "ip4s-core_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/comcast/ip4s-core_2.13/3.0.4/ip4s-core_2.13-3.0.4.jar"}, {"name": "ip4s-core_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/comcast/ip4s-core_2.13/3.0.4/ip4s-core_2.13-3.0.4-sources.jar"}]}, {"organization": "org.typelevel", "name": "literally_2.13", "version": "1.0.2", "configurations": "default", "artifacts": [{"name": "literally_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/literally_2.13/1.0.2/literally_2.13-1.0.2.jar"}, {"name": "literally_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/literally_2.13/1.0.2/literally_2.13-1.0.2-sources.jar"}]}, {"organization": "org.log4s", "name": "log4s_2.13", "version": "1.10.0", "configurations": "default", "artifacts": [{"name": "log4s_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/log4s/log4s_2.13/1.10.0/log4s_2.13-1.10.0.jar"}, {"name": "log4s_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/log4s/log4s_2.13/1.10.0/log4s_2.13-1.10.0-sources.jar"}]}, {"organization": "org.scodec", "name": "scodec-bits_2.13", "version": "1.1.29", "configurations": "default", "artifacts": [{"name": "scodec-bits_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scodec/scodec-bits_2.13/1.1.29/scodec-bits_2.13-1.1.29.jar"}, {"name": "scodec-bits_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scodec/scodec-bits_2.13/1.1.29/scodec-bits_2.13-1.1.29-sources.jar"}]}, {"organization": "org.typelevel", "name": "vault_2.13", "version": "3.1.0", "configurations": "default", "artifacts": [{"name": "vault_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/vault_2.13/3.1.0/vault_2.13-3.1.0.jar"}, {"name": "vault_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/vault_2.13/3.1.0/vault_2.13-3.1.0-sources.jar"}]}, {"organization": "org.typelevel", "name": "jawn-fs2_2.13", "version": "2.1.0", "configurations": "default", "artifacts": [{"name": "jawn-fs2_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/jawn-fs2_2.13/2.1.0/jawn-fs2_2.13-2.1.0.jar"}, {"name": "jawn-fs2_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/jawn-fs2_2.13/2.1.0/jawn-fs2_2.13-2.1.0-sources.jar"}]}, {"organization": "org.typelevel", "name": "jawn-parser_2.13", "version": "1.2.0", "configurations": "default", "artifacts": [{"name": "jawn-parser_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/jawn-parser_2.13/1.2.0/jawn-parser_2.13-1.2.0.jar"}, {"name": "jawn-parser_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/jawn-parser_2.13/1.2.0/jawn-parser_2.13-1.2.0-sources.jar"}]}, {"organization": "io.circe", "name": "circe-numbers_2.13", "version": "0.15.0-M1", "configurations": "default", "artifacts": [{"name": "circe-numbers_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-numbers_2.13/0.15.0-M1/circe-numbers_2.13-0.15.0-M1.jar"}, {"name": "circe-numbers_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-numbers_2.13/0.15.0-M1/circe-numbers_2.13-0.15.0-M1-sources.jar"}]}, {"organization": "org.typelevel", "name": "cats-free_2.13", "version": "2.6.1", "configurations": "default", "artifacts": [{"name": "cats-free_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-free_2.13/2.6.1/cats-free_2.13-2.6.1.jar"}, {"name": "cats-free_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-free_2.13/2.6.1/cats-free_2.13-2.6.1-sources.jar"}]}, {"organization": "io.getquill", "name": "quill-sql-portable_2.13", "version": "3.11.0", "configurations": "default", "artifacts": [{"name": "quill-sql-portable_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/getquill/quill-sql-portable_2.13/3.11.0/quill-sql-portable_2.13-3.11.0.jar"}, {"name": "quill-sql-portable_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/getquill/quill-sql-portable_2.13/3.11.0/quill-sql-portable_2.13-3.11.0-sources.jar"}]}, {"organization": "com.github.vertical-blank", "name": "scala-sql-formatter_2.13", "version": "1.0.1", "configurations": "default", "artifacts": [{"name": "scala-sql-formatter_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/github/vertical-blank/scala-sql-formatter_2.13/1.0.1/scala-sql-formatter_2.13-1.0.1.jar"}, {"name": "scala-sql-formatter_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/github/vertical-blank/scala-sql-formatter_2.13/1.0.1/scala-sql-formatter_2.13-1.0.1-sources.jar"}]}, {"organization": "com.lihaoyi", "name": "fansi_2.13", "version": "0.2.9", "configurations": "default", "artifacts": [{"name": "fansi_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/fansi_2.13/0.2.9/fansi_2.13-0.2.9.jar"}, {"name": "fansi_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/fansi_2.13/0.2.9/fansi_2.13-0.2.9-sources.jar"}]}, {"organization": "com.twitter", "name": "chill_2.13", "version": "0.10.0", "configurations": "default", "artifacts": [{"name": "chill_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/twitter/chill_2.13/0.10.0/chill_2.13-0.10.0.jar"}, {"name": "chill_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/twitter/chill_2.13/0.10.0/chill_2.13-0.10.0-sources.jar"}]}, {"organization": "io.suzaku", "name": "boopickle_2.13", "version": "1.3.1", "configurations": "default", "artifacts": [{"name": "boopickle_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/suzaku/boopickle_2.13/1.3.1/boopickle_2.13-1.3.1.jar"}, {"name": "boopickle_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/suzaku/boopickle_2.13/1.3.1/boopickle_2.13-1.3.1-sources.jar"}]}, {"organization": "dev.zio", "name": "zio_2.13", "version": "1.0.12", "configurations": "default", "artifacts": [{"name": "zio_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/dev/zio/zio_2.13/1.0.12/zio_2.13-1.0.12.jar"}, {"name": "zio_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/dev/zio/zio_2.13/1.0.12/zio_2.13-1.0.12-sources.jar"}]}, {"organization": "dev.zio", "name": "zio-streams_2.13", "version": "1.0.12", "configurations": "default", "artifacts": [{"name": "zio-streams_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/dev/zio/zio-streams_2.13/1.0.12/zio-streams_2.13-1.0.12.jar"}, {"name": "zio-streams_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/dev/zio/zio-streams_2.13/1.0.12/zio-streams_2.13-1.0.12-sources.jar"}]}, {"organization": "org.typelevel", "name": "cats-kernel_2.13", "version": "2.6.1", "configurations": "default", "artifacts": [{"name": "cats-kernel_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-kernel_2.13/2.6.1/cats-kernel_2.13-2.6.1.jar"}, {"name": "cats-kernel_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-kernel_2.13/2.6.1/cats-kernel_2.13-2.6.1-sources.jar"}]}, {"organization": "org.typelevel", "name": "simulacrum-scalafix-annotations_2.13", "version": "0.5.4", "configurations": "default", "artifacts": [{"name": "simulacrum-scalafix-annotations_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/simulacrum-scalafix-annotations_2.13/0.5.4/simulacrum-scalafix-annotations_2.13-0.5.4.jar"}, {"name": "simulacrum-scalafix-annotations_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/simulacrum-scalafix-annotations_2.13/0.5.4/simulacrum-scalafix-annotations_2.13-0.5.4-sources.jar"}]}, {"organization": "com.github.vertical-blank", "name": "sql-formatter", "version": "1.0", "configurations": "default", "artifacts": [{"name": "sql-formatter", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/github/vertical-blank/sql-formatter/1.0/sql-formatter-1.0.jar"}, {"name": "sql-formatter", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/github/vertical-blank/sql-formatter/1.0/sql-formatter-1.0-sources.jar"}]}, {"organization": "com.twitter", "name": "chill-java", "version": "0.10.0", "configurations": "default", "artifacts": [{"name": "chill-java", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/twitter/chill-java/0.10.0/chill-java-0.10.0.jar"}, {"name": "chill-java", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/twitter/chill-java/0.10.0/chill-java-0.10.0-sources.jar"}]}, {"organization": "com.esotericsoftware", "name": "kryo-shaded", "version": "4.0.2", "configurations": "default", "artifacts": [{"name": "kryo-shaded", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/esotericsoftware/kryo-shaded/4.0.2/kryo-shaded-4.0.2.jar"}, {"name": "kryo-shaded", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/esotericsoftware/kryo-shaded/4.0.2/kryo-shaded-4.0.2-sources.jar"}]}, {"organization": "org.apache.xbean", "name": "xbean-asm7-shaded", "version": "4.15", "configurations": "default", "artifacts": [{"name": "xbean-asm7-shaded", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/apache/xbean/xbean-asm7-shaded/4.15/xbean-asm7-shaded-4.15.jar"}, {"name": "xbean-asm7-shaded", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/apache/xbean/xbean-asm7-shaded/4.15/xbean-asm7-shaded-4.15-sources.jar"}]}, {"organization": "dev.zio", "name": "zio-stacktracer_2.13", "version": "1.0.12", "configurations": "default", "artifacts": [{"name": "zio-stacktracer_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/dev/zio/zio-stacktracer_2.13/1.0.12/zio-stacktracer_2.13-1.0.12.jar"}, {"name": "zio-stacktracer_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/dev/zio/zio-stacktracer_2.13/1.0.12/zio-stacktracer_2.13-1.0.12-sources.jar"}]}, {"organization": "dev.zio", "name": "izumi-reflect_2.13", "version": "1.1.3", "configurations": "default", "artifacts": [{"name": "izumi-reflect_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/dev/zio/izumi-reflect_2.13/1.1.3/izumi-reflect_2.13-1.1.3.jar"}, {"name": "izumi-reflect_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/dev/zio/izumi-reflect_2.13/1.1.3/izumi-reflect_2.13-1.1.3-sources.jar"}]}, {"organization": "com.esotericsoftware", "name": "minlog", "version": "1.3.0", "configurations": "default", "artifacts": [{"name": "minlog", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/esotericsoftware/minlog/1.3.0/minlog-1.3.0.jar"}, {"name": "minlog", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/esotericsoftware/minlog/1.3.0/minlog-1.3.0-sources.jar"}]}, {"organization": "org.objenesis", "name": "obje<PERSON><PERSON>", "version": "2.5.1", "configurations": "default", "artifacts": [{"name": "obje<PERSON><PERSON>", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/objenesis/objenesis/2.5.1/objenesis-2.5.1.jar"}, {"name": "obje<PERSON><PERSON>", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/objenesis/objenesis/2.5.1/objenesis-2.5.1-sources.jar"}]}, {"organization": "dev.zio", "name": "izumi-reflect-thirdparty-boopickle-shaded_2.13", "version": "1.1.3", "configurations": "default", "artifacts": [{"name": "izumi-reflect-thirdparty-boopickle-shaded_2.13", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/dev/zio/izumi-reflect-thirdparty-boopickle-shaded_2.13/1.1.3/izumi-reflect-thirdparty-boopickle-shaded_2.13-1.1.3.jar"}, {"name": "izumi-reflect-thirdparty-boopickle-shaded_2.13", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/dev/zio/izumi-reflect-thirdparty-boopickle-shaded_2.13/1.1.3/izumi-reflect-thirdparty-boopickle-shaded_2.13-1.1.3-sources.jar"}]}, {"organization": "org.scala-lang", "name": "scala-library", "version": "2.13.9", "configurations": "optional", "artifacts": [{"name": "scala-library", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.9/scala-library-2.13.9.jar"}, {"name": "scala-library", "classifier": "sources", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.9/scala-library-2.13.9-sources.jar"}]}]}, "tags": ["library"]}}