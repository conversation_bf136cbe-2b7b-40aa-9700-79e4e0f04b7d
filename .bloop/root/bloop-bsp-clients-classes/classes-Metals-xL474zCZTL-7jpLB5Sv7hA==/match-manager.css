html, body {
    background-color: rgb(248, 248, 248);
    font-family: "Helvetica Neue", "Segoe UI", Helvetica, Arial, sans-serif;
    height: 100%;
    margin: 0;
}

h1 {
    font-size: 18px;
    font-weight: 400;
    line-height: 40px;
    margin: 0;
    visibility: visible;
    white-space: nowrap;
}

h1 a {
    color: rgb(255, 255, 255);
    text-decoration: none;
}

header {
    background-color: rgb(37, 120, 207);
    color: rgb(255, 255, 255);
    padding: 0 12px;
}

section {
    height: calc(100% - 40px);
    overflow: scroll;
    padding: 0;
    position: relative;
}

table {
    background-color: rgb(248, 248, 248);
    border-collapse: separate;
    border-spacing: 0;
    display: block;
    font-size: 16px;
    margin: 12px;
    min-width: calc(100% - 12px);
    padding: 0;
}

thead {
    position: sticky;
    top: 0;
}

tfoot, tfoot td, tfoot tr:last-child td {
    background-color: rgb(250, 250, 250);
    border: 1px solid rgb(230, 230, 230);
    bottom: 0;
    color: rgb(35, 112, 194);
    position: sticky;
}

th {
    background-color: rgb(250, 250, 250);
    border: 1px solid rgb(230, 230, 230);
    border-left-width: 0;
    color: rgb(35, 112, 194);
    margin: 0;
    padding: 12px;
    position: sticky;
    top: 0;
}

th:first-child {
    border-left-width: 1px;
}

th:hover {
    background-color: rgb(242, 242, 242);
}

td {
    background-color: rgb(255, 255, 255);
    border: solid rgb(242, 242, 242);
    border-width: 0 1px 1px 0;
    color: rgb(64, 64, 64);
    padding: 12px;
}

td.group a {
    color: rgb(35, 112, 194);
}

td.group:hover {
    background-color: rgb(242, 242, 242);
}

tbody td {
    height: 100%;
    padding: 0;
}

tbody td a {
    align-items: center;
    color: rgb(64, 64, 64);
    display: flex;
    height: calc(100% - 24px);
    padding: 14px 12px 9px 12px;
    text-decoration: none;
    width: calc(100% - 24px);
}

td:first-child {
    border-left-width: 1px;
}

tr:last-child td {
    border-bottom: none;
}

tr:hover td {
    background-color: rgb(250, 250, 250);
}

form {
    background-color: rgb(255, 255, 255);
    border: 1px solid rgb(230, 230, 230);
    display: block;
    left: 12px;
    margin: 12px 0 12px 12px;
    overflow: hidden;
    padding: 12px;
    position: sticky;
    white-space: nowrap;
}

td form {
    display: inline-block;
    margin: 0;
    margin-right: 12px;
    padding-right: 0;
}

td form:last-child {
}

input[type=text] {
    border: 1px solid rgb(242, 242, 242);
    border-radius: 0;
    display: inline-block;
    font-size: 13px;
    height: 32px;
    line-height: 15px;
    margin: 0;
    min-height: 30px;
    outline: none;
    padding: 0 8px;
    position: relative;
    top: 1px;
    width: 300px;
}
#search input[type=text] {
    top:0;
}

input[type=text]:focus {
    border-color: #888;
    -webkit-box-shadow: inset 3px 3px 3px -1px rgba(0, 0, 0, 0.2);
    box-shadow: inset 3px 3px 3px -1px rgba(0, 0, 0, 0.2);
    height: 32px;
    outline: none;
    position: relative;
    z-index: 1;
}

button, select {
    background-color: rgb(250, 250, 250);
    border: 1px solid rgb(230, 230, 230);
    cursor: pointer;
    display: inline-block;
    font-size: 12px;
    line-height: 16px;
    margin-top: 1px;
    padding: 8px 12px;
    position: relative;
    text-decoration: none;
    height: 34px;
}

button {
    text-align: center;
    margin: 0 0 0 -1px;
}

button:last-child {
    border-bottom-right-radius: 3px;
    border-top-right-radius: 3px;
    margin-right: 12px;
}

button:first-child, input[type=text]:first-child {
    border-bottom-left-radius: 3px;
    border-top-left-radius: 3px;
}

select {
    border-radius: 3px 0 0 3px;
    text-align: left;
    line-height: 16px;
    height: 34px;
}

button:hover, select:hover {
    background-color: rgb(242, 242, 242);
    outline: none !important;
}

button:focus, select:focus {
    background-color: rgb(230, 230, 230);
    border-color: #888;
    z-index: 1;
    outline: none !important;
}

span.navigation {
    background: rgb(250, 250, 250);
    display: inline-block;
}

tfoot td > span {
    left: 0;
    margin-left: -12px;
    padding-left: 12px;
    position: sticky;
}

p.flash-message {
    margin: 12px;
}