# Private Key Authentication Setup

This application uses **private key authentication only** for Snowflake database connections via **environment variable content**. No file paths or password authentication are supported.

## Prerequisites

1. **Generate RSA Key Pair** (minimum 2048-bit):
   ```bash
   # Generate private key (unencrypted)
   openssl genrsa 2048 | openssl pkcs8 -topk8 -inform PEM -out rsa_key.p8 -nocrypt
   
   # Generate public key
   openssl rsa -in rsa_key.p8 -pubout -out rsa_key.pub
   ```

2. **Configure Snowflake User** with the public key:
   ```sql
   -- Extract the public key content (excluding header/footer)
   -- and assign it to your Snowflake user
   ALTER USER your_username SET RSA_PUBLIC_KEY='MIIBIjANBgkqhkiG9w0BAQEFA...';
   ```

3. **Get Private Key Content**:
   ```bash
   # Copy the entire content of your private key file
   cat rsa_key.p8
   ```

## Environment Variables

**Required for all environments:**
```bash
export DATABASE_USER=your_username
export DATABASE_CONNECTION_STRING=****************************************************/
export PRIVATE_KEY_CONTENT="-----BEGIN PRIVATE KEY-----
MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC...
-----END PRIVATE KEY-----"
```

## Local Development Setup

For local development, update your `.env` file in the project root:

```properties
DATABASE_USER=your_username
DATABASE_CONNECTION_STRING=**************************************************************************************************************

# Private key authentication (content as string)
PRIVATE_KEY_CONTENT="-----BEGIN PRIVATE KEY-----
MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC...
-----END PRIVATE KEY-----"
```

## Configuration Details

The application **always uses private key authentication** via environment variable content. The following environment variables are required:

- **`DATABASE_USER`**: Your Snowflake username
- **`DATABASE_CONNECTION_STRING`**: Snowflake JDBC connection string  
- **`PRIVATE_KEY_CONTENT`**: Full private key content as a string (including headers/footers)

## Security Notes

1. **File Permissions**: Ensure your private key file has restricted permissions:
   ```bash
   chmod 600 /path/to/rsa_key.p8
   ```

2. **Key Storage**: Store private keys securely and never commit them to version control.

3. **Passphrase**: If using encrypted keys, store the passphrase securely (e.g., in a secrets manager).

4. **Path Format**: Use forward slashes (`/`) for file paths on all operating systems, including Windows.

## Troubleshooting

1. **Key Format Errors**: Ensure your private key is in PKCS#8 format (`.p8` extension).

2. **Connection Failures**: Verify that:
   - The public key is correctly assigned to your Snowflake user
   - The private key file path is accessible
   - The passphrase is correct (if using encrypted keys)

3. **Permission Errors**: Check file permissions on the private key file.

## Kubernetes Deployment

The Kubernetes deployment uses the private key content directly from the secret:

- **Environment variable**: `PRIVATE_KEY_CONTENT`
- **Secret key**: `snowflake.privatekey` in the `match-manager` secret
- **No file mounting required**

Make sure your Kubernetes secret contains the private key content:
```bash
kubectl create secret generic match-manager \
  --from-literal=snowflake.username=your_username \
  --from-literal=snowflake.privatekey="$(cat /path/to/rsa_key.p8)"
```

## Example Docker/Kubernetes Configuration

```yaml
env:
  - name: PRIVATE_KEY_CONTENT
    valueFrom:
      secretKeyRef:
        name: match-manager
        key: snowflake.privatekey
  - name: DATABASE_USER
    valueFrom:
      secretKeyRef:
        name: match-manager
        key: snowflake.username
  - name: DATABASE_CONNECTION_STRING
    value: "****************************************************/"
```
