package nl.dpgmedia.matchmanager

import org.ekrich.config.{Config, ConfigFactory}

object Configuration {
  private val config: Config = ConfigFactory.load()

  object Database {

    val user: String =
      config.getString("nl.dpgmedia.matchmanager.database.user")

    val connectionString: String =
      config.getString("nl.dpgmedia.matchmanager.database.connectionString")

    val privateKeyContent: String =
      config.getString("nl.dpgmedia.matchmanager.database.privateKeyContent")

    // Database configuration details
    val databaseName: String =
      config.getStringOr("nl.dpgmedia.matchmanager.database.databaseName", "test")

    val schemaName: String =
      config.getStringOr("nl.dpgmedia.matchmanager.database.schemaName", "online_services_recruitment_private")

    val tableName: String =
      config.getStringOr("nl.dpgmedia.matchmanager.database.tableName", "src_match_manager_cra_organization_group_overrides")

  }

  // Helper extension for optional config values
  private implicit class ConfigOps(config: Config) {

    def getStringOr(path: String, default: String): String =
      if (config.hasPath(path)) config.getString(path) else default

  }

}
