package nl.dpgmedia.matchmanager

import cats.effect._
import doobie._
import doobie.hikari._
import com.zaxxer.hikari.HikariConfig

trait Database[F[_]] {

  def transactor(
    databaseConfiguration: Configuration.Database.type
  ): Resource[F, HikariTransactor[F]]

}

object Database {

  def apply[F[_]: Async]: Database[F] = new Database[F] {

    override def transactor(
      databaseConfiguration: Configuration.Database.type
    ): Resource[F, HikariTransactor[F]] =
      for {
        ce <- ExecutionContexts.fixedThreadPool[F](
          Runtime.getRuntime.availableProcessors()
        )
        xa <- {
          println(s"Configured Snowflake properties:")
          println(s"  - user: ${databaseConfiguration.user}")
          println(s"  - base JDBC URL: ${databaseConfiguration.connectionString}")
          println(s"  - database: ${databaseConfiguration.databaseName}")
          println(s"  - schema: ${databaseConfiguration.schemaName}")
          println(s"  - table: ${databaseConfiguration.tableName}")

          // Extract the base64 content from PEM format (remove headers and newlines)
          val privateKeyContent = databaseConfiguration.privateKeyContent
            .replaceAll("-----BEGIN PRIVATE KEY-----", "")
            .replaceAll("-----END PRIVATE KEY-----", "")
            .replaceAll("\\s", "") // Remove all whitespace including newlines

          // URL encode the base64 private key for safe inclusion in URL
          val privateKeyUrlEncoded = java.net.URLEncoder.encode(privateKeyContent, "UTF-8")

          // Build JDBC URL with authentication parameters
          val jdbcUrlWithAuth = if (databaseConfiguration.connectionString.contains("?")) {
            s"${databaseConfiguration.connectionString}&user=${databaseConfiguration.user}&authenticator=snowflake_jwt&private_key_base64=$privateKeyUrlEncoded"
          } else {
            s"${databaseConfiguration.connectionString}?user=${databaseConfiguration.user}&authenticator=snowflake_jwt&private_key_base64=$privateKeyUrlEncoded"
          }

          println(s"  - Using private_key_base64 with snowflake_jwt authenticator (length: ${privateKeyContent.length} chars)")
          println(s"  - Complete JDBC URL length: ${jdbcUrlWithAuth.length} chars")

          val config = new HikariConfig()
          config.setJdbcUrl(jdbcUrlWithAuth)
          config.setDriverClassName("net.snowflake.client.jdbc.SnowflakeDriver")

          config.setMaximumPoolSize(10)
          config.setMinimumIdle(2)
          config.setConnectionTimeout(30000)
          config.setIdleTimeout(600000)
          config.setMaxLifetime(1800000)

          HikariTransactor.fromHikariConfig[F](config, ce)
        }
      } yield xa

  }

}
