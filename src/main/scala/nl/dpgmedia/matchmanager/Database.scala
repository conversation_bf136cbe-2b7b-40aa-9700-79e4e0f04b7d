package nl.dpgmedia.matchmanager

import cats.effect._
import doobie._
import doobie.hikari._
import com.zaxxer.hikari.HikariConfig
import java.security.KeyFactory
import java.security.spec.PKCS8EncodedKeySpec
import java.util.{Base64, Properties}

trait Database[F[_]] {

  def transactor(
    databaseConfiguration: Configuration.Database.type
  ): Resource[F, HikariTransactor[F]]

}

object Database {

  def apply[F[_]: Async]: Database[F] = new Database[F] {

    override def transactor(
      databaseConfiguration: Configuration.Database.type
    ): Resource[F, HikariTransactor[F]] =
      for {
        ce <- ExecutionContexts.fixedThreadPool[F](
          Runtime.getRuntime.availableProcessors()
        )
        xa <- {
          println(s"Configured Snowflake properties:")
          println(s"  - user: ${databaseConfiguration.user}")
          println(s"  - base JDBC URL: ${databaseConfiguration.connectionString}")
          println(s"  - database: ${databaseConfiguration.databaseName}")
          println(s"  - schema: ${databaseConfiguration.schemaName}")
          println(s"  - table: ${databaseConfiguration.tableName}")

          // Parse the private key from PEM format into a Java PrivateKey object
          val privateKeyBase64Content = databaseConfiguration.privateKeyContent
            .replaceAll("-----BEGIN PRIVATE KEY-----", "")
            .replaceAll("-----END PRIVATE KEY-----", "")
            .replaceAll("\\s", "") // Remove all whitespace including newlines

          // Decode the base64 content and create a PrivateKey object
          val keyBytes = Base64.getDecoder.decode(privateKeyBase64Content)
          val keySpec = new PKCS8EncodedKeySpec(keyBytes)
          val keyFactory = KeyFactory.getInstance("RSA")
          val privateKey = keyFactory.generatePrivate(keySpec)

          println(s"  - Using private key authentication with snowflake_jwt authenticator")
          println(s"  - Private key type: ${privateKey.getClass.getName}")
          println(s"  - Private key algorithm: ${privateKey.getAlgorithm}")

          // Create Properties object to pass the PrivateKey directly
          val props = new Properties()
          props.put("user", databaseConfiguration.user)
          props.put("authenticator", "snowflake_jwt")
          props.put("privatekey", privateKey)

          val config = new HikariConfig()
          config.setJdbcUrl(databaseConfiguration.connectionString)
          config.setDataSourceProperties(props)
          config.setDriverClassName("net.snowflake.client.jdbc.SnowflakeDriver")

          config.setMaximumPoolSize(10)
          config.setMinimumIdle(2)
          config.setConnectionTimeout(30000)
          config.setIdleTimeout(600000)
          config.setMaxLifetime(1800000)

          HikariTransactor.fromHikariConfig[F](config, ce)
        }
      } yield xa

  }

}
