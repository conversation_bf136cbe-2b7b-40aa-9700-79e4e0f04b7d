apiVersion: apps/v1
kind: Deployment
metadata:
  name: match-manager
  namespace: {{NAMESPACE}}
  annotations:
    jenkins/build-number: "{{BUILD_NUMBER}}"
spec:
  selector:
    matchLabels:
      app: match-manager
  replicas: 2
  revisionHistoryLimit: 5
  progressDeadlineSeconds: 320
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: match-manager
    spec:
      containers:
      - name: match-manager
        image: 674201978047.dkr.ecr.eu-west-1.amazonaws.com/match-manager:{{BUILD_NUMBER}}
        resources:
          limits:
            memory: 400Mi
          requests:
            cpu: 100m
            memory: 250Mi
        ports:
          - name: app-port
            containerPort: 8080
        readinessProbe:
          timeoutSeconds: 10
          initialDelaySeconds: 30
          periodSeconds: 15
          failureThreshold: 4
          httpGet:
            path: "/"
            port: app-port
        livenessProbe:
          timeoutSeconds: 10
          initialDelaySeconds: 30
          periodSeconds: 15
          failureThreshold: 4
          httpGet:
            path: "/"
            port: app-port
        envFrom:
        - configMapRef:
            name: match-manager-config
        env:
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: DATABASE_USER
          valueFrom:
            secretKeyRef:
              name: match-manager
              key: snowflake.username
        - name: PRIVATE_KEY_CONTENT
          valueFrom:
            secretKeyRef:
              name: match-manager
              key: snowflake.privatekey
      restartPolicy: Always
      dnsPolicy: Default
